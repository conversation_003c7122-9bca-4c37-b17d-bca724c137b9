// Export Manager for handling different export formats

class ExportManager {
    constructor() {
        this.exportFormats = CONFIG.EXPORT_FORMATS;
        this.results = [];
    }
    
    // Set results to export
    setResults(results) {
        this.results = results;
    }
    
    // Export to HTML format
    async exportToHTML() {
        try {
            const htmlContent = this.generateHTMLContent();
            const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
            this.downloadFile(blob, 'نتائج_التحليل.html');
            
            this.showSuccessMessage('تم تصدير ملف HTML بنجاح');
        } catch (error) {
            this.showErrorMessage('خطأ في تصدير ملف HTML: ' + error.message);
        }
    }
    
    // Export to Excel format
    async exportToExcel() {
        try {
            if (typeof XLSX === 'undefined') {
                // Fallback to CSV
                const workbookData = this.generateExcelData();
                const csvContent = this.convertToCSV(workbookData);
                const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' });
                this.downloadFile(blob, 'نتائج_التحليل.csv');
                this.showSuccessMessage('تم تصدير ملف CSV بنجاح (Excel غير متوفر)');
                return;
            }

            // Create workbook using SheetJS
            const workbook = XLSX.utils.book_new();

            // Main results sheet
            const mainData = this.generateExcelData();
            const mainSheet = XLSX.utils.aoa_to_sheet(mainData);

            // Set column widths
            mainSheet['!cols'] = [
                { width: 30 }, // اسم الملف
                { width: 15 }, // نوع الملف
                { width: 15 }, // الحجم
                { width: 20 }, // تاريخ التعديل
                { width: 50 }  // المحتوى المعدل
            ];

            XLSX.utils.book_append_sheet(workbook, mainSheet, 'النتائج الرئيسية');

            // Create individual sheets for each file
            this.results.forEach((result, index) => {
                if (index < 10) { // Limit to first 10 files to avoid too many sheets
                    const fileName = (result.originalName || `ملف_${index + 1}`).substring(0, 20);
                    const content = result.processedContent || result.content || '';

                    // Split content into lines for better Excel display
                    const lines = content.split('\n').map(line => [line]);
                    const sheet = XLSX.utils.aoa_to_sheet([['المحتوى المعالج'], ...lines]);

                    sheet['!cols'] = [{ width: 80 }];
                    XLSX.utils.book_append_sheet(workbook, sheet, fileName);
                }
            });

            // Generate Excel file
            const excelBuffer = XLSX.write(workbook, {
                bookType: 'xlsx',
                type: 'array',
                bookSST: false
            });

            const blob = new Blob([excelBuffer], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });

            this.downloadFile(blob, 'نتائج_التحليل.xlsx');
            this.showSuccessMessage('تم تصدير ملف Excel بنجاح');
        } catch (error) {
            this.showErrorMessage('خطأ في تصدير ملف Excel: ' + error.message);
        }
    }
    
    // Export to PDF format
    async exportToPDF() {
        try {
            if (typeof jsPDF === 'undefined') {
                // Fallback to print method
                const pdfContent = this.generatePDFContent();
                const printWindow = window.open('', '_blank');

                printWindow.document.write(pdfContent);
                printWindow.document.close();

                printWindow.focus();
                setTimeout(() => {
                    printWindow.print();
                    printWindow.close();
                }, 500);

                this.showSuccessMessage('تم فتح نافذة الطباعة لحفظ PDF');
                return;
            }

            // Use jsPDF for better PDF generation
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4'
            });

            // Add Arabic font support (simplified)
            doc.setFont('helvetica');
            doc.setFontSize(16);

            let yPosition = 20;
            const pageHeight = doc.internal.pageSize.height;
            const margin = 20;

            // Title
            doc.text('نتائج تحليل وتعديل الملفات', margin, yPosition);
            yPosition += 10;
            doc.setFontSize(12);
            doc.text(`تاريخ الإنشاء: ${new Date().toLocaleString('ar-SA')}`, margin, yPosition);
            yPosition += 20;

            // Process results
            this.results.forEach((result, index) => {
                if (yPosition > pageHeight - 40) {
                    doc.addPage();
                    yPosition = 20;
                }

                doc.setFontSize(14);
                doc.text(`ملف ${index + 1}: ${result.originalName || 'غير محدد'}`, margin, yPosition);
                yPosition += 10;

                doc.setFontSize(10);
                const content = (result.processedContent || result.content || '').substring(0, 500);
                const lines = doc.splitTextToSize(content, 170);

                lines.forEach(line => {
                    if (yPosition > pageHeight - 20) {
                        doc.addPage();
                        yPosition = 20;
                    }
                    doc.text(line, margin, yPosition);
                    yPosition += 5;
                });

                yPosition += 10;
            });

            doc.save('نتائج_التحليل.pdf');
            this.showSuccessMessage('تم تصدير ملف PDF بنجاح');
        } catch (error) {
            this.showErrorMessage('خطأ في تصدير ملف PDF: ' + error.message);
        }
    }
    
    // Export to TXT format
    async exportToTXT() {
        try {
            const txtContent = this.generateTXTContent();
            const blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8' });
            this.downloadFile(blob, 'نتائج_التحليل.txt');
            
            this.showSuccessMessage('تم تصدير ملف TXT بنجاح');
        } catch (error) {
            this.showErrorMessage('خطأ في تصدير ملف TXT: ' + error.message);
        }
    }
    
    // Export to ZIP format
    async exportToZIP() {
        try {
            if (typeof JSZip === 'undefined') {
                this.showErrorMessage('مكتبة JSZip غير متوفرة');
                return;
            }

            const zip = new JSZip();

            // Add HTML file
            const htmlContent = this.generateHTMLContent();
            zip.file('نتائج_التحليل.html', htmlContent);

            // Add CSV file
            const csvData = this.generateExcelData();
            const csvContent = '\ufeff' + this.convertToCSV(csvData);
            zip.file('نتائج_التحليل.csv', csvContent);

            // Add TXT file
            const txtContent = this.generateTXTContent();
            zip.file('نتائج_التحليل.txt', txtContent);

            // Add individual processed files
            const processedFolder = zip.folder('الملفات_المعالجة');
            this.results.forEach((result, index) => {
                const fileName = result.originalName || `ملف_${index + 1}`;
                const extension = result.fileType?.extension || '.txt';
                const content = result.processedContent || result.content || '';

                processedFolder.file(`${fileName}_معالج${extension}`, content);
            });

            // Generate ZIP file
            const zipBlob = await zip.generateAsync({
                type: 'blob',
                compression: 'DEFLATE',
                compressionOptions: { level: 6 }
            });

            this.downloadFile(zipBlob, 'نتائج_التحليل.zip');
            this.showSuccessMessage('تم تصدير ملف ZIP بنجاح');
        } catch (error) {
            this.showErrorMessage('خطأ في تصدير ملف ZIP: ' + error.message);
        }
    }
    
    // Generate HTML content
    generateHTMLContent() {
        let html = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج تحليل وتعديل الملفات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: #f8f9fa; }
        .search-box { position: sticky; top: 20px; z-index: 100; }
        .result-item { margin-bottom: 2rem; }
        .highlight { background-color: yellow; }
        .no-results { display: none; text-align: center; padding: 2rem; }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">نتائج تحليل وتعديل الملفات</h1>
                <p class="text-center text-muted mb-4">تم إنشاء هذا التقرير في: ${new Date().toLocaleString('ar-SA')}</p>
                
                <!-- Search Box -->
                <div class="search-box mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" 
                                       placeholder="البحث في النتائج..." onkeyup="searchResults()">
                                <button class="btn btn-primary" onclick="searchResults()">
                                    <i class="fas fa-search"></i> بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Results -->
                <div id="resultsContainer">
        `;
        
        this.results.forEach((result, index) => {
            html += `
                <div class="result-item card shadow-sm" data-index="${index}">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            ${result.originalName || `ملف ${index + 1}`}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>نوع الملف:</strong> ${result.fileType?.extension || 'غير محدد'}<br>
                                <strong>الحجم:</strong> ${this.formatFileSize(result.size || 0)}<br>
                                <strong>تاريخ التعديل:</strong> ${result.lastModified ? new Date(result.lastModified).toLocaleString('ar-SA') : 'غير محدد'}
                            </div>
                            <div class="col-md-9">
                                <h6>المحتوى المعدل:</h6>
                                <pre class="bg-light p-3 rounded" style="white-space: pre-wrap; max-height: 400px; overflow-y: auto;">${this.escapeHtml(result.processedContent || result.content || '')}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
                
                <div class="no-results">
                    <div class="alert alert-info">
                        <i class="fas fa-search me-2"></i>
                        لم يتم العثور على نتائج مطابقة لبحثك
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function searchResults() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const resultItems = document.querySelectorAll('.result-item');
            const noResults = document.querySelector('.no-results');
            let visibleCount = 0;
            
            resultItems.forEach(item => {
                const content = item.textContent.toLowerCase();
                if (content.includes(searchTerm)) {
                    item.style.display = 'block';
                    visibleCount++;
                    
                    // Highlight search term
                    if (searchTerm) {
                        highlightText(item, searchTerm);
                    }
                } else {
                    item.style.display = 'none';
                }
            });
            
            noResults.style.display = visibleCount === 0 && searchTerm ? 'block' : 'none';
        }
        
        function highlightText(element, searchTerm) {
            // Simple highlighting implementation
            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );
            
            const textNodes = [];
            let node;
            
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }
            
            textNodes.forEach(textNode => {
                const parent = textNode.parentNode;
                if (parent.tagName !== 'SCRIPT' && parent.tagName !== 'STYLE') {
                    const text = textNode.textContent;
                    const regex = new RegExp(searchTerm, 'gi');
                    if (regex.test(text)) {
                        const highlightedText = text.replace(regex, '<span class="highlight">$&</span>');
                        const wrapper = document.createElement('div');
                        wrapper.innerHTML = highlightedText;
                        parent.replaceChild(wrapper, textNode);
                    }
                }
            });
        }
    </script>
</body>
</html>
        `;
        
        return html;
    }
    
    // Generate Excel data
    generateExcelData() {
        const data = [
            ['اسم الملف', 'نوع الملف', 'الحجم', 'تاريخ التعديل', 'المحتوى المعدل']
        ];
        
        this.results.forEach(result => {
            data.push([
                result.originalName || 'غير محدد',
                result.fileType?.extension || 'غير محدد',
                this.formatFileSize(result.size || 0),
                result.lastModified ? new Date(result.lastModified).toLocaleString('ar-SA') : 'غير محدد',
                (result.processedContent || result.content || '').substring(0, 1000) + '...'
            ]);
        });
        
        return data;
    }
    
    // Generate PDF content
    generatePDFContent() {
        let content = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>نتائج تحليل وتعديل الملفات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .result { margin-bottom: 30px; page-break-inside: avoid; }
        .result-header { background: #f0f0f0; padding: 10px; border: 1px solid #ccc; }
        .result-content { padding: 15px; border: 1px solid #ccc; border-top: none; }
        pre { white-space: pre-wrap; font-size: 12px; }
        @media print { body { margin: 0; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>نتائج تحليل وتعديل الملفات</h1>
        <p>تاريخ الإنشاء: ${new Date().toLocaleString('ar-SA')}</p>
    </div>
        `;
        
        this.results.forEach((result, index) => {
            content += `
    <div class="result">
        <div class="result-header">
            <h3>ملف ${index + 1}: ${result.originalName || 'غير محدد'}</h3>
            <p>نوع الملف: ${result.fileType?.extension || 'غير محدد'} | 
               الحجم: ${this.formatFileSize(result.size || 0)} | 
               تاريخ التعديل: ${result.lastModified ? new Date(result.lastModified).toLocaleString('ar-SA') : 'غير محدد'}</p>
        </div>
        <div class="result-content">
            <pre>${this.escapeHtml(result.processedContent || result.content || '')}</pre>
        </div>
    </div>
            `;
        });
        
        content += `
</body>
</html>
        `;
        
        return content;
    }
    
    // Generate TXT content
    generateTXTContent() {
        let content = `نتائج تحليل وتعديل الملفات\n`;
        content += `تاريخ الإنشاء: ${new Date().toLocaleString('ar-SA')}\n`;
        content += `عدد الملفات: ${this.results.length}\n`;
        content += `${'='.repeat(50)}\n\n`;
        
        this.results.forEach((result, index) => {
            content += `ملف ${index + 1}: ${result.originalName || 'غير محدد'}\n`;
            content += `نوع الملف: ${result.fileType?.extension || 'غير محدد'}\n`;
            content += `الحجم: ${this.formatFileSize(result.size || 0)}\n`;
            content += `تاريخ التعديل: ${result.lastModified ? new Date(result.lastModified).toLocaleString('ar-SA') : 'غير محدد'}\n`;
            content += `${'-'.repeat(30)}\n`;
            content += `المحتوى المعدل:\n`;
            content += `${result.processedContent || result.content || ''}\n`;
            content += `${'='.repeat(50)}\n\n`;
        });
        
        return content;
    }
    
    // Generate all formats for ZIP
    async generateAllFormats() {
        const files = [];
        
        // HTML file
        files.push({
            name: 'نتائج_التحليل.html',
            content: this.generateHTMLContent(),
            type: 'text/html'
        });
        
        // CSV file
        const csvData = this.generateExcelData();
        files.push({
            name: 'نتائج_التحليل.csv',
            content: '\ufeff' + this.convertToCSV(csvData),
            type: 'text/csv'
        });
        
        // TXT file
        files.push({
            name: 'نتائج_التحليل.txt',
            content: this.generateTXTContent(),
            type: 'text/plain'
        });
        
        return files;
    }
    
    // Convert data to CSV format
    convertToCSV(data) {
        return data.map(row => 
            row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
        ).join('\n');
    }
    
    // Create simple ZIP (placeholder - in real implementation use JSZip)
    createSimpleZip(files) {
        // This is a placeholder - in a real implementation, you would use JSZip library
        let zipContent = 'PK\x03\x04'; // ZIP file signature
        
        files.forEach(file => {
            zipContent += file.content;
        });
        
        return zipContent;
    }
    
    // Download file
    downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }
    
    // Utility functions
    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }
    
    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }
    
    showMessage(message, type) {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        document.body.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }
}

// Initialize Export Manager
const exportManager = new ExportManager();

// Register module as loaded
if (typeof window !== 'undefined' && window.loadingManager) {
    window.loadingManager.markLoaded('ExportManager');
}
