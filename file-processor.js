// File Processor for handling different file types

class FileProcessor {
    constructor() {
        this.supportedTypes = CONFIG.SUPPORTED_FILE_TYPES;
        this.processors = {
            text: this.processTextFile.bind(this),
            html: this.processHtmlFile.bind(this),
            markdown: this.processMarkdownFile.bind(this),
            excel: this.processExcelFile.bind(this),
            batch: this.processBatchFile.bind(this),
            javascript: this.processJavaScriptFile.bind(this),
            css: this.processCssFile.bind(this),
            json: this.processJsonFile.bind(this),
            xml: this.processXmlFile.bind(this),
            csv: this.processCsvFile.bind(this)
        };
    }
    
    // Main processing method
    async processFile(file, instructions, presetOptions = []) {
        try {
            const fileType = this.getFileType(file);
            if (!fileType) {
                throw new Error(`نوع الملف غير مدعوم: ${file.type}`);
            }
            
            const content = await this.readFileContent(file);
            const processor = this.processors[fileType.processor];
            
            if (!processor) {
                throw new Error(`معالج غير متوفر لنوع الملف: ${fileType.processor}`);
            }
            
            // Apply preset instructions first
            let processedContent = content;
            if (presetOptions.length > 0) {
                processedContent = await this.applyPresetInstructions(processedContent, presetOptions, fileType);
            }
            
            // Apply custom instructions if provided
            if (instructions && instructions.trim()) {
                processedContent = await processor(processedContent, instructions, file.name);
            }
            
            return {
                originalName: file.name,
                processedContent: processedContent,
                fileType: fileType,
                size: file.size,
                lastModified: file.lastModified
            };
            
        } catch (error) {
            console.error('خطأ في معالجة الملف:', error);
            throw error;
        }
    }
    
    // Get file type configuration
    getFileType(file) {
        return this.supportedTypes[file.type] || null;
    }
    
    // Read file content
    async readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                resolve(e.target.result);
            };
            
            reader.onerror = (e) => {
                reject(new Error('خطأ في قراءة الملف'));
            };
            
            // Handle different file types
            if (file.type.includes('excel') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                reader.readAsArrayBuffer(file);
            } else {
                reader.readAsText(file, 'UTF-8');
            }
        });
    }
    
    // Apply preset instructions
    async applyPresetInstructions(content, presetOptions, fileType) {
        let processedContent = content;
        
        for (const option of presetOptions) {
            switch (option) {
                case 'remove-empty-lines':
                    processedContent = this.removeEmptyLines(processedContent);
                    break;
                case 'remove-duplicates':
                    processedContent = this.removeDuplicateLines(processedContent);
                    break;
                case 'remove-links':
                    processedContent = this.removeLinks(processedContent);
                    break;
                case 'remove-html-codes':
                    processedContent = this.removeHtmlCodes(processedContent);
                    break;
                case 'compress-code':
                    processedContent = this.compressCode(processedContent, fileType);
                    break;
                case 'improve-seo':
                    processedContent = await this.improveSEO(processedContent, fileType);
                    break;
                case 'improve-formatting':
                    processedContent = this.improveFormatting(processedContent, fileType);
                    break;
            }
        }
        
        return processedContent;
    }
    
    // Text file processor
    async processTextFile(content, instructions, fileName) {
        const prompt = this.createPrompt(content, instructions, 'text', fileName);
        return await apiManager.makeRequest(prompt);
    }
    
    // HTML file processor
    async processHtmlFile(content, instructions, fileName) {
        const prompt = this.createPrompt(content, instructions, 'html', fileName);
        return await apiManager.makeRequest(prompt);
    }
    
    // Markdown file processor
    async processMarkdownFile(content, instructions, fileName) {
        const prompt = this.createPrompt(content, instructions, 'markdown', fileName);
        return await apiManager.makeRequest(prompt);
    }
    
    // Excel file processor
    async processExcelFile(content, instructions, fileName) {
        // For Excel files, we need to convert to text first
        const textContent = await this.convertExcelToText(content);
        const prompt = this.createPrompt(textContent, instructions, 'excel', fileName);
        return await apiManager.makeRequest(prompt);
    }
    
    // Batch file processor
    async processBatchFile(content, instructions, fileName) {
        const prompt = this.createPrompt(content, instructions, 'batch', fileName);
        return await apiManager.makeRequest(prompt);
    }
    
    // JavaScript file processor
    async processJavaScriptFile(content, instructions, fileName) {
        const prompt = this.createPrompt(content, instructions, 'javascript', fileName);
        return await apiManager.makeRequest(prompt);
    }
    
    // CSS file processor
    async processCssFile(content, instructions, fileName) {
        const prompt = this.createPrompt(content, instructions, 'css', fileName);
        return await apiManager.makeRequest(prompt);
    }
    
    // JSON file processor
    async processJsonFile(content, instructions, fileName) {
        const prompt = this.createPrompt(content, instructions, 'json', fileName);
        return await apiManager.makeRequest(prompt);
    }
    
    // XML file processor
    async processXmlFile(content, instructions, fileName) {
        const prompt = this.createPrompt(content, instructions, 'xml', fileName);
        return await apiManager.makeRequest(prompt);
    }
    
    // CSV file processor
    async processCsvFile(content, instructions, fileName) {
        const prompt = this.createPrompt(content, instructions, 'csv', fileName);
        return await apiManager.makeRequest(prompt);
    }
    
    // Create AI prompt
    createPrompt(content, instructions, fileType, fileName) {
        return `أنت محرر ملفات احترافي متخصص. مهمتك هي تعديل المحتوى التالي وفقاً للتعليمات المحددة مع الحفاظ على التنسيق والبنية الأصلية.

معلومات الملف:
- اسم الملف: ${fileName}
- نوع الملف: ${fileType}

التعليمات:
${instructions}

المحتوى الأصلي:
${content}

متطلبات مهمة:
1. احتفظ بالتنسيق والبنية الأصلية للملف
2. لا تغير إلا ما هو مطلوب في التعليمات فقط
3. تأكد من صحة ودقة التعديلات 100%
4. لا تضف أي تعليقات أو توضيحات من الذكاء الصناعي
5. أظهر المحتوى المعدل فقط بدون مقدمات

المحتوى المعدل:`;
    }
    
    // Utility methods for preset instructions
    removeEmptyLines(content) {
        return content.replace(/^\s*[\r\n]/gm, '');
    }
    
    removeDuplicateLines(content) {
        const lines = content.split('\n');
        const uniqueLines = [...new Set(lines)];
        return uniqueLines.join('\n');
    }
    
    removeLinks(content) {
        // Remove HTTP/HTTPS links
        return content.replace(/https?:\/\/[^\s]+/g, '');
    }
    
    removeHtmlCodes(content) {
        // Remove HTML tags and entities
        return content
            .replace(/<[^>]*>/g, '')
            .replace(/&[a-zA-Z0-9#]+;/g, '');
    }
    
    compressCode(content, fileType) {
        if (fileType.processor === 'css') {
            return content
                .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
                .replace(/\s+/g, ' ') // Compress whitespace
                .replace(/;\s*}/g, '}') // Remove last semicolon
                .trim();
        } else if (fileType.processor === 'javascript') {
            return content
                .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
                .replace(/\/\/.*$/gm, '') // Remove line comments
                .replace(/\s+/g, ' ') // Compress whitespace
                .trim();
        }
        return content;
    }
    
    async improveSEO(content, fileType) {
        if (fileType.processor === 'html') {
            const prompt = `حسن السيو للمحتوى HTML التالي:
${content}

متطلبات:
- أضف meta tags مناسبة
- حسن العناوين H1, H2, H3
- أضف alt text للصور
- حسن البنية الدلالية`;
            
            return await apiManager.makeRequest(prompt);
        }
        return content;
    }
    
    improveFormatting(content, fileType) {
        if (fileType.processor === 'html') {
            // Basic HTML formatting
            return content
                .replace(/></g, '>\n<')
                .replace(/^\s+|\s+$/gm, '')
                .split('\n')
                .map(line => line.trim())
                .filter(line => line.length > 0)
                .join('\n');
        }
        return content;
    }
    
    // Convert Excel to text using SheetJS
    async convertExcelToText(arrayBuffer) {
        try {
            if (typeof XLSX === 'undefined') {
                throw new Error('مكتبة XLSX غير متوفرة');
            }

            const workbook = XLSX.read(arrayBuffer, { type: 'array' });
            let textContent = '';

            // Process each worksheet
            workbook.SheetNames.forEach((sheetName, index) => {
                const worksheet = workbook.Sheets[sheetName];

                textContent += `=== ورقة العمل: ${sheetName} ===\n`;

                // Convert to CSV format for better text representation
                const csvData = XLSX.utils.sheet_to_csv(worksheet);
                textContent += csvData + '\n\n';
            });

            return textContent;
        } catch (error) {
            console.error('خطأ في تحويل ملف Excel:', error);
            return `خطأ في تحويل ملف Excel: ${error.message}`;
        }
    }
}

// Initialize File Processor
const fileProcessor = new FileProcessor();
