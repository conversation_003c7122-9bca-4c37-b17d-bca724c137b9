<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة تحليل وتعديل الملفات بالذكاء الصناعي - محرر ملفات احترافي</title>
    <meta name="description" content="أداة احترافية لتحليل وتعديل ملفات HTML, TXT, Markdown, Excel, BAT باستخدام الذكاء الصناعي مع إمكانيات متقدمة للتعامل مع الملفات الكبيرة">
    <meta name="keywords" content="تحليل ملفات, تعديل ملفات, ذكاء صناعي, HTML, TXT, Markdown, Excel, BAT">
    <meta name="author" content="أداة تحليل الملفات">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="أداة تحليل وتعديل الملفات بالذكاء الصناعي">
    <meta property="og:description" content="أداة احترافية لتحليل وتعديل ملفات HTML, TXT, Markdown, Excel, BAT باستخدام الذكاء الصناعي">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="ar_AR">
    
    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "أداة تحليل وتعديل الملفات بالذكاء الصناعي",
        "description": "أداة احترافية لتحليل وتعديل ملفات HTML, TXT, Markdown, Excel, BAT باستخدام الذكاء الصناعي",
        "applicationCategory": "DeveloperApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "author": {
            "@type": "Organization",
            "name": "أداة تحليل الملفات"
        }
    }
    </script>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="main-title">
                        <i class="fas fa-robot text-primary me-3"></i>
                        أداة تحليل وتعديل الملفات بالذكاء الصناعي
                    </h1>
                    <p class="subtitle">محرر ملفات احترافي متقدم يدعم جميع أنواع الملفات</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="header-actions">
                        <button class="btn btn-outline-primary me-2" id="importSettings">
                            <i class="fas fa-upload"></i> استيراد الإعدادات
                        </button>
                        <button class="btn btn-outline-secondary" id="exportSettings">
                            <i class="fas fa-download"></i> تصدير الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- File Upload Section -->
            <section class="upload-section mb-5">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-cloud-upload-alt me-2"></i>
                            رفع الملفات
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <h4>اسحب الملفات هنا أو انقر للاختيار</h4>
                                <p>يدعم: HTML, TXT, Markdown, Excel, BAT وملفات أخرى</p>
                                <input type="file" id="fileInput" multiple accept=".html,.txt,.md,.xlsx,.xls,.bat,.js,.css,.json,.xml,.csv">
                            </div>
                        </div>
                        <div class="uploaded-files mt-3" id="uploadedFiles"></div>
                    </div>
                </div>
            </section>

            <!-- Instructions Section -->
            <section class="instructions-section mb-5">
                <div class="card shadow-lg">
                    <div class="card-header bg-success text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i>
                            تعليمات التعديل
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="customInstructions" class="form-label">تعليمات مخصصة:</label>
                                <textarea class="form-control" id="customInstructions" rows="6" 
                                    placeholder="اكتب تعليماتك المخصصة هنا..."></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">التعليمات المسبقة:</label>
                                <div class="preset-instructions" id="presetInstructions">
                                    <!-- Will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Processing Section -->
            <section class="processing-section mb-5">
                <div class="card shadow-lg">
                    <div class="card-header bg-warning text-dark">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            معالجة الملفات
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="processing-controls mb-4">
                            <button class="btn btn-primary btn-lg me-3" id="startProcessing">
                                <i class="fas fa-play me-2"></i>
                                بدء المعالجة
                            </button>
                            <button class="btn btn-warning me-3" id="pauseProcessing" disabled>
                                <i class="fas fa-pause me-2"></i>
                                إيقاف مؤقت
                            </button>
                            <button class="btn btn-danger" id="stopProcessing" disabled>
                                <i class="fas fa-stop me-2"></i>
                                إيقاف
                            </button>
                        </div>
                        
                        <!-- Progress Bar -->
                        <div class="progress-container mb-4" id="progressContainer" style="display: none;">
                            <div class="progress-info mb-2">
                                <span class="progress-text" id="progressText">جاري المعالجة...</span>
                                <span class="progress-percentage" id="progressPercentage">0%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="processing-details mt-2">
                                <small class="text-muted" id="processingDetails">
                                    الملف الحالي: - | المتبقي: - | الوقت المقدر: -
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section mb-5" id="resultsSection" style="display: none;">
                <div class="card shadow-lg">
                    <div class="card-header bg-info text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            النتائج
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="results-controls mb-3">
                            <input type="text" class="form-control mb-3" id="searchResults" 
                                   placeholder="البحث في النتائج...">
                        </div>
                        <div class="results-content" id="resultsContent">
                            <!-- Results will be populated here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Export Section -->
            <section class="export-section mb-5" id="exportSection" style="display: none;">
                <div class="card shadow-lg">
                    <div class="card-header bg-secondary text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-download me-2"></i>
                            تصدير النتائج
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="export-options">
                            <button class="btn btn-outline-primary me-2 mb-2" id="exportHTML">
                                <i class="fas fa-code me-2"></i>HTML
                            </button>
                            <button class="btn btn-outline-success me-2 mb-2" id="exportExcel">
                                <i class="fas fa-file-excel me-2"></i>Excel
                            </button>
                            <button class="btn btn-outline-danger me-2 mb-2" id="exportPDF">
                                <i class="fas fa-file-pdf me-2"></i>PDF
                            </button>
                            <button class="btn btn-outline-info me-2 mb-2" id="exportTXT">
                                <i class="fas fa-file-alt me-2"></i>TXT
                            </button>
                            <button class="btn btn-outline-warning" id="exportZIP">
                                <i class="fas fa-file-archive me-2"></i>ZIP
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer-section">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 أداة تحليل وتعديل الملفات. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>مدعوم بتقنية الذكاء الصناعي المتقدمة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <h5 id="loadingText">جاري المعالجة...</h5>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SheetJS for Excel support -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- JSZip for ZIP export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <!-- jsPDF for PDF export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- Custom JS -->
    <script src="config.js"></script>
    <script src="api-manager.js"></script>
    <script src="file-processor.js"></script>
    <script src="content-splitter.js"></script>
    <script src="preset-instructions.js"></script>
    <script src="export-manager.js"></script>
    <script src="settings-manager.js"></script>
    <script src="script.js"></script>
</body>
</html>
