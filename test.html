<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أداة تحليل الملفات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #2563eb;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d1fae5; color: #065f46; }
        .error { background: #fee2e2; color: #991b1b; }
        .info { background: #dbeafe; color: #1e40af; }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border-left: 4px solid #2563eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">اختبار أداة تحليل وتعديل الملفات</h1>
        <p>هذه الصفحة تحتوي على اختبارات للتأكد من عمل جميع وظائف الأداة بشكل صحيح.</p>
        
        <button onclick="runAllTests()">تشغيل جميع الاختبارات</button>
        <button onclick="clearResults()">مسح النتائج</button>
        
        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2 class="test-title">ملفات اختبار نموذجية</h2>
        
        <h3>1. ملف BAT للاختبار</h3>
        <pre>@echo off
start "" "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --profile-directory="Profile 6"</pre>
        
        <h3>2. ملف HTML للاختبار</h3>
        <pre>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
    &lt;title&gt;صفحة اختبار&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;مرحبا بالعالم&lt;/h1&gt;
    &lt;p&gt;هذه صفحة اختبار&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;</pre>
        
        <h3>3. ملف Markdown للاختبار</h3>
        <pre># عنوان رئيسي

## عنوان فرعي

هذا نص عادي مع **نص عريض** و *نص مائل*.

- عنصر قائمة 1
- عنصر قائمة 2
- عنصر قائمة 3

[رابط اختبار](https://example.com)</pre>
        
        <h3>4. ملف JavaScript للاختبار</h3>
        <pre>function testFunction() {
    console.log("Hello World");
    var x = 10;
    var y = 20;
    return x + y;
}

// تعليق اختبار
testFunction();</pre>
    </div>

    <script>
        let testResults = [];
        
        function addTestResult(testName, status, message) {
            const result = {
                name: testName,
                status: status,
                message: message,
                timestamp: new Date().toLocaleString('ar-SA')
            };
            testResults.push(result);
            displayResults();
        }
        
        function displayResults() {
            const container = document.getElementById('testResults');
            let html = '<h3>نتائج الاختبارات:</h3>';
            
            testResults.forEach((result, index) => {
                const statusClass = result.status === 'success' ? 'success' : 
                                  result.status === 'error' ? 'error' : 'info';
                
                html += `
                    <div class="test-result ${statusClass}">
                        <strong>${result.name}</strong> - ${result.status.toUpperCase()}<br>
                        ${result.message}<br>
                        <small>الوقت: ${result.timestamp}</small>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function clearResults() {
            testResults = [];
            document.getElementById('testResults').innerHTML = '';
        }
        
        async function runAllTests() {
            clearResults();
            addTestResult('بدء الاختبارات', 'info', 'تم بدء تشغيل جميع الاختبارات...');
            
            // Test 1: Check if CONFIG is loaded
            try {
                if (typeof CONFIG !== 'undefined') {
                    addTestResult('تحميل الإعدادات', 'success', 'تم تحميل ملف الإعدادات بنجاح');
                } else {
                    addTestResult('تحميل الإعدادات', 'error', 'فشل في تحميل ملف الإعدادات');
                }
            } catch (error) {
                addTestResult('تحميل الإعدادات', 'error', 'خطأ: ' + error.message);
            }
            
            // Test 2: Check API Manager
            try {
                if (typeof APIManager !== 'undefined') {
                    addTestResult('مدير APIs', 'success', 'تم تحميل مدير APIs بنجاح');
                } else {
                    addTestResult('مدير APIs', 'error', 'فشل في تحميل مدير APIs');
                }
            } catch (error) {
                addTestResult('مدير APIs', 'error', 'خطأ: ' + error.message);
            }
            
            // Test 3: Check File Processor
            try {
                if (typeof FileProcessor !== 'undefined') {
                    addTestResult('معالج الملفات', 'success', 'تم تحميل معالج الملفات بنجاح');
                } else {
                    addTestResult('معالج الملفات', 'error', 'فشل في تحميل معالج الملفات');
                }
            } catch (error) {
                addTestResult('معالج الملفات', 'error', 'خطأ: ' + error.message);
            }
            
            // Test 4: Check Content Splitter
            try {
                if (typeof ContentSplitter !== 'undefined') {
                    addTestResult('مقسم المحتوى', 'success', 'تم تحميل مقسم المحتوى بنجاح');
                } else {
                    addTestResult('مقسم المحتوى', 'error', 'فشل في تحميل مقسم المحتوى');
                }
            } catch (error) {
                addTestResult('مقسم المحتوى', 'error', 'خطأ: ' + error.message);
            }
            
            // Test 5: Check Preset Instructions
            try {
                if (typeof PresetInstructionsManager !== 'undefined') {
                    addTestResult('التعليمات المسبقة', 'success', 'تم تحميل مدير التعليمات المسبقة بنجاح');
                } else {
                    addTestResult('التعليمات المسبقة', 'error', 'فشل في تحميل مدير التعليمات المسبقة');
                }
            } catch (error) {
                addTestResult('التعليمات المسبقة', 'error', 'خطأ: ' + error.message);
            }
            
            // Test 6: Check Export Manager
            try {
                if (typeof ExportManager !== 'undefined') {
                    addTestResult('مدير التصدير', 'success', 'تم تحميل مدير التصدير بنجاح');
                } else {
                    addTestResult('مدير التصدير', 'error', 'فشل في تحميل مدير التصدير');
                }
            } catch (error) {
                addTestResult('مدير التصدير', 'error', 'خطأ: ' + error.message);
            }
            
            // Test 7: Check Settings Manager
            try {
                if (typeof SettingsManager !== 'undefined') {
                    addTestResult('مدير الإعدادات', 'success', 'تم تحميل مدير الإعدادات بنجاح');
                } else {
                    addTestResult('مدير الإعدادات', 'error', 'فشل في تحميل مدير الإعدادات');
                }
            } catch (error) {
                addTestResult('مدير الإعدادات', 'error', 'خطأ: ' + error.message);
            }
            
            // Test 8: Check External Libraries
            const libraries = [
                { name: 'Bootstrap', check: () => typeof bootstrap !== 'undefined' },
                { name: 'SheetJS', check: () => typeof XLSX !== 'undefined' },
                { name: 'JSZip', check: () => typeof JSZip !== 'undefined' },
                { name: 'jsPDF', check: () => typeof jsPDF !== 'undefined' }
            ];
            
            libraries.forEach(lib => {
                try {
                    if (lib.check()) {
                        addTestResult(`مكتبة ${lib.name}`, 'success', `تم تحميل مكتبة ${lib.name} بنجاح`);
                    } else {
                        addTestResult(`مكتبة ${lib.name}`, 'error', `فشل في تحميل مكتبة ${lib.name}`);
                    }
                } catch (error) {
                    addTestResult(`مكتبة ${lib.name}`, 'error', 'خطأ: ' + error.message);
                }
            });
            
            // Test 9: Test Local Storage
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                
                if (value === 'value') {
                    addTestResult('التخزين المحلي', 'success', 'التخزين المحلي يعمل بشكل صحيح');
                } else {
                    addTestResult('التخزين المحلي', 'error', 'مشكلة في التخزين المحلي');
                }
            } catch (error) {
                addTestResult('التخزين المحلي', 'error', 'خطأ: ' + error.message);
            }
            
            // Test 10: Test File API Support
            try {
                if (window.File && window.FileReader && window.FileList && window.Blob) {
                    addTestResult('دعم File API', 'success', 'المتصفح يدعم File API');
                } else {
                    addTestResult('دعم File API', 'error', 'المتصفح لا يدعم File API');
                }
            } catch (error) {
                addTestResult('دعم File API', 'error', 'خطأ: ' + error.message);
            }
            
            addTestResult('انتهاء الاختبارات', 'info', 'تم الانتهاء من جميع الاختبارات');
        }
        
        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
