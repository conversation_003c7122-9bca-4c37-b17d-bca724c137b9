<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة تحليل وتعديل الملفات - صفحة البداية</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .logo {
            font-size: 4rem;
            color: #2563eb;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        .title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 15px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #64748b;
            margin-bottom: 40px;
        }
        
        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .option-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            border-color: #2563eb;
        }
        
        .option-card.recommended {
            border-color: #10b981;
            background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
        }
        
        .option-card.recommended::before {
            content: "⭐ مُوصى به";
            position: absolute;
            top: -10px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .option-card {
            position: relative;
        }
        
        .option-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .recommended .option-icon {
            color: #10b981;
        }
        
        .option-card:not(.recommended) .option-icon {
            color: #2563eb;
        }
        
        .option-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #1e293b;
        }
        
        .option-description {
            color: #64748b;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .option-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }
        
        .recommended .option-button {
            background: #10b981;
        }
        
        .option-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .features {
            background: #f8fafc;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
            text-align: right;
        }
        
        .features h3 {
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        
        .feature-icon {
            color: #10b981;
            margin-left: 10px;
            font-size: 1.2rem;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            color: #64748b;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 20px;
                padding: 30px 20px;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <i class="fas fa-robot"></i>
        </div>
        
        <h1 class="title">أداة تحليل وتعديل الملفات</h1>
        <p class="subtitle">بالذكاء الصناعي المتقدم</p>
        
        <div class="options">
            <div class="option-card recommended" onclick="openApp()">
                <div class="option-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h3 class="option-title">النسخة الكاملة</h3>
                <p class="option-description">
                    الأداة الكاملة في ملف واحد - تعمل فوراً بدون مشاكل تحميل
                </p>
                <button class="option-button">🚀 ابدأ الآن</button>
            </div>
            
            <div class="option-card" onclick="openDiagnostic()">
                <div class="option-icon">
                    <i class="fas fa-stethoscope"></i>
                </div>
                <h3 class="option-title">أداة التشخيص</h3>
                <p class="option-description">
                    فحص شامل للنظام والملفات المطلوبة
                </p>
                <button class="option-button">🔍 فحص النظام</button>
            </div>
            
            <div class="option-card" onclick="openTest()">
                <div class="option-icon">
                    <i class="fas fa-flask"></i>
                </div>
                <h3 class="option-title">صفحة الاختبار</h3>
                <p class="option-description">
                    اختبار جميع المكونات والوظائف
                </p>
                <button class="option-button">🧪 تشغيل الاختبارات</button>
            </div>
        </div>
        
        <div class="features">
            <h3>✨ المميزات الرئيسية</h3>
            <div class="features-grid">
                <div class="feature-item">
                    <i class="fas fa-check-circle feature-icon"></i>
                    <span>دعم 10+ أنواع ملفات</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle feature-icon"></i>
                    <span>معالجة بالذكاء الصناعي</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle feature-icon"></i>
                    <span>تعليمات مسبقة جاهزة</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle feature-icon"></i>
                    <span>إنشاء متغيرات BAT</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle feature-icon"></i>
                    <span>تصدير متعدد التنسيقات</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle feature-icon"></i>
                    <span>واجهة عربية احترافية</span>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 أداة تحليل وتعديل الملفات - مدعوم بتقنية الذكاء الصناعي المتقدمة</p>
        </div>
    </div>
    
    <script>
        function openApp() {
            // Check if app.html exists
            fetch('app.html', { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        window.location.href = 'app.html';
                    } else {
                        alert('ملف app.html غير موجود. يرجى التأكد من وجود الملف في نفس المجلد.');
                    }
                })
                .catch(() => {
                    // If fetch fails, try to open anyway
                    window.location.href = 'app.html';
                });
        }
        
        function openDiagnostic() {
            window.open('diagnostic.html', '_blank');
        }
        
        function openTest() {
            window.open('test.html', '_blank');
        }
        
        // Auto-redirect to app.html if it exists and user doesn't interact within 10 seconds
        let autoRedirectTimer = setTimeout(() => {
            const userConfirmed = confirm('هل تريد الانتقال تلقائياً للنسخة الكاملة؟');
            if (userConfirmed) {
                openApp();
            }
        }, 10000);
        
        // Cancel auto-redirect if user interacts with the page
        document.addEventListener('click', () => {
            clearTimeout(autoRedirectTimer);
        });
        
        document.addEventListener('keydown', () => {
            clearTimeout(autoRedirectTimer);
        });
        
        // Add some interactive effects
        document.querySelectorAll('.option-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
