// Configuration file for the File Analysis and Editing Tool

const CONFIG = {
    // Gemini API Configuration
    GEMINI_API_KEYS: [
        'AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ',
        'AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg',
        'AIzaSyCH5jU2QrczZOLPqNsceh_um6qs_N3Skgw',
        'AIzaSyCTYMFClrP1A1by2rTXTRYjl3mi-FfVvak',
        'AIzaSyDzSRdhUClIXUWpi9QRKi0hfB29_bzaQAc',
        'AIzaSyBzlVUgjCkG44mm3_8Rt-ZiLskXsxM29SM',
        'AIzaSyDeSqeSAb4QZ6JEf_LbLTrnVOgcGMSb7ds',
        'AIzaSyAu5ToET8qHvDUPzrDOvkr1T99qZXvZORk',
        'AIzaSyAFu5lHg2W5ibpFbm-k3kNlW-rI96JcQOg',
        'AIzaSyBThniUP9oFxyriMOV4-LpvmJtW3u1gZCQ',
        'AIzaSyAns8tVQ-V6sqbh7UPQB7tqYgWAZUIN6Z4',
        'AIzaSyCS32gY-wcVTRIuE2PY_3ppVloGmvFlAqo',
        'AIzaSyCc3Y4hHWuAxKtJx4E0h-spwYUP6Eqzk4k',
        'AIzaSyAmxg1f8A3s1mXxdUav0zeyC52BF-5QBoA',
        'AIzaSyD7C9Ry7EeCXwvrLkymXuUp3FwqbqloFuQ'
    ],
    
    GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
    
    // Token and Request Limits
    MAX_TOKENS_PER_REQUEST: 30000,
    MAX_REQUESTS_PER_MINUTE: 60,
    MAX_REQUESTS_PER_DAY: 1500,
    
    // Content Splitting Configuration
    CHUNK_SIZE: 25000, // Characters per chunk
    OVERLAP_SIZE: 1000, // Overlap between chunks
    
    // File Processing Configuration
    SUPPORTED_FILE_TYPES: {
        'text/plain': { extension: '.txt', icon: 'fas fa-file-alt', processor: 'text' },
        'text/html': { extension: '.html', icon: 'fab fa-html5', processor: 'html' },
        'text/markdown': { extension: '.md', icon: 'fab fa-markdown', processor: 'markdown' },
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { 
            extension: '.xlsx', icon: 'fas fa-file-excel', processor: 'excel' 
        },
        'application/vnd.ms-excel': { extension: '.xls', icon: 'fas fa-file-excel', processor: 'excel' },
        'application/x-bat': { extension: '.bat', icon: 'fas fa-terminal', processor: 'batch' },
        'text/javascript': { extension: '.js', icon: 'fab fa-js-square', processor: 'javascript' },
        'text/css': { extension: '.css', icon: 'fab fa-css3-alt', processor: 'css' },
        'application/json': { extension: '.json', icon: 'fas fa-code', processor: 'json' },
        'application/xml': { extension: '.xml', icon: 'fas fa-code', processor: 'xml' },
        'text/csv': { extension: '.csv', icon: 'fas fa-file-csv', processor: 'csv' }
    },
    
    // Processing States
    PROCESSING_STATES: {
        IDLE: 'idle',
        PROCESSING: 'processing',
        PAUSED: 'paused',
        COMPLETED: 'completed',
        ERROR: 'error'
    },
    
    // Export Formats
    EXPORT_FORMATS: {
        HTML: 'html',
        EXCEL: 'excel',
        PDF: 'pdf',
        TXT: 'txt',
        ZIP: 'zip'
    },
    
    // UI Messages
    MESSAGES: {
        UPLOAD_SUCCESS: 'تم رفع الملف بنجاح',
        UPLOAD_ERROR: 'خطأ في رفع الملف',
        PROCESSING_START: 'بدء معالجة الملفات...',
        PROCESSING_COMPLETE: 'تم الانتهاء من معالجة جميع الملفات',
        PROCESSING_ERROR: 'حدث خطأ أثناء المعالجة',
        API_LIMIT_REACHED: 'تم الوصول لحد استخدام API، جاري التبديل...',
        NO_FILES_SELECTED: 'يرجى اختيار ملف واحد على الأقل',
        NO_INSTRUCTIONS: 'يرجى إدخال تعليمات التعديل',
        EXPORT_SUCCESS: 'تم تصدير الملف بنجاح',
        EXPORT_ERROR: 'خطأ في تصدير الملف'
    },
    
    // Default Settings
    DEFAULT_SETTINGS: {
        autoSave: true,
        showProgress: true,
        enableNotifications: true,
        maxConcurrentRequests: 3,
        retryAttempts: 3,
        retryDelay: 2000,
        theme: 'light',
        language: 'ar'
    },
    
    // Retry Configuration
    RETRY_CONFIG: {
        maxAttempts: 3,
        baseDelay: 1000,
        maxDelay: 10000,
        backoffFactor: 2
    },
    
    // Rate Limiting
    RATE_LIMIT: {
        requestsPerSecond: 2,
        burstLimit: 5,
        windowSize: 60000 // 1 minute
    },
    
    // Content Processing Rules
    PROCESSING_RULES: {
        preserveFormatting: true,
        maintainStructure: true,
        respectOriginalContent: true,
        onlyModifyRequested: true
    },
    
    // Quality Assurance
    QUALITY_CHECKS: {
        validateOutput: true,
        checkFormatting: true,
        verifyInstructions: true,
        ensureCompleteness: true
    }
};

// Utility functions for configuration
const ConfigUtils = {
    // Get file type configuration
    getFileTypeConfig(mimeType) {
        return CONFIG.SUPPORTED_FILE_TYPES[mimeType] || null;
    },
    
    // Get file icon based on extension
    getFileIcon(extension) {
        const fileType = Object.values(CONFIG.SUPPORTED_FILE_TYPES)
            .find(type => type.extension === extension);
        return fileType ? fileType.icon : 'fas fa-file';
    },
    
    // Check if file type is supported
    isFileTypeSupported(mimeType) {
        return CONFIG.SUPPORTED_FILE_TYPES.hasOwnProperty(mimeType);
    },
    
    // Get processing limits
    getProcessingLimits() {
        return {
            maxTokens: CONFIG.MAX_TOKENS_PER_REQUEST,
            maxRequests: CONFIG.MAX_REQUESTS_PER_MINUTE,
            chunkSize: CONFIG.CHUNK_SIZE
        };
    },
    
    // Get retry configuration
    getRetryConfig() {
        return { ...CONFIG.RETRY_CONFIG };
    },
    
    // Get rate limit configuration
    getRateLimitConfig() {
        return { ...CONFIG.RATE_LIMIT };
    },
    
    // Validate API key format
    isValidApiKey(apiKey) {
        return typeof apiKey === 'string' && 
               apiKey.startsWith('AIzaSy') && 
               apiKey.length === 39;
    },
    
    // Get random API key
    getRandomApiKey() {
        const validKeys = CONFIG.GEMINI_API_KEYS.filter(key => this.isValidApiKey(key));
        return validKeys[Math.floor(Math.random() * validKeys.length)];
    },
    
    // Calculate optimal chunk size based on content
    calculateOptimalChunkSize(contentLength) {
        if (contentLength <= CONFIG.CHUNK_SIZE) {
            return contentLength;
        }
        
        const numChunks = Math.ceil(contentLength / CONFIG.CHUNK_SIZE);
        return Math.ceil(contentLength / numChunks);
    },
    
    // Get processing timeout based on content size
    getProcessingTimeout(contentLength) {
        const baseTimeout = 30000; // 30 seconds
        const additionalTime = Math.ceil(contentLength / 1000) * 1000; // 1 second per 1000 chars
        return Math.min(baseTimeout + additionalTime, 300000); // Max 5 minutes
    }
};

// Export configuration for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, ConfigUtils };
}

// Register module as loaded
if (typeof window !== 'undefined' && window.loadingManager) {
    window.loadingManager.markLoaded('CONFIG');
}
