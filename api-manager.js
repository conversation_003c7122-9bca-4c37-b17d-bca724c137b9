// API Manager for handling Gemini API requests with rotation and rate limiting

class APIManager {
    constructor() {
        this.apiKeys = [...CONFIG.GEMINI_API_KEYS];
        this.currentKeyIndex = 0;
        this.requestCounts = new Map();
        this.rateLimiter = new RateLimiter();
        this.retryManager = new RetryManager();
        this.lastRequestTime = 0;
        this.requestQueue = [];
        this.isProcessingQueue = false;
        
        // Initialize request counts for each API key
        this.apiKeys.forEach(key => {
            this.requestCounts.set(key, {
                daily: 0,
                hourly: 0,
                minute: 0,
                lastReset: {
                    daily: new Date().setHours(0, 0, 0, 0),
                    hourly: new Date().setMinutes(0, 0, 0),
                    minute: new Date().setSeconds(0, 0)
                }
            });
        });
        
        // Reset counters periodically
        this.startCounterResetTimer();
    }
    
    // Get current API key
    getCurrentApiKey() {
        return this.apiKeys[this.currentKeyIndex];
    }
    
    // Rotate to next API key
    rotateApiKey() {
        this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
        console.log(`تم التبديل إلى API Key رقم: ${this.currentKeyIndex + 1}`);
    }
    
    // Check if current API key has reached limits
    isCurrentKeyLimited() {
        const currentKey = this.getCurrentApiKey();
        const counts = this.requestCounts.get(currentKey);
        
        return counts.daily >= CONFIG.MAX_REQUESTS_PER_DAY ||
               counts.minute >= CONFIG.MAX_REQUESTS_PER_MINUTE;
    }
    
    // Find available API key
    findAvailableApiKey() {
        const startIndex = this.currentKeyIndex;
        
        do {
            if (!this.isCurrentKeyLimited()) {
                return this.getCurrentApiKey();
            }
            this.rotateApiKey();
        } while (this.currentKeyIndex !== startIndex);
        
        return null; // All keys are limited
    }
    
    // Update request count for current key
    updateRequestCount() {
        const currentKey = this.getCurrentApiKey();
        const counts = this.requestCounts.get(currentKey);
        
        counts.daily++;
        counts.hourly++;
        counts.minute++;
    }
    
    // Reset counters based on time
    resetCounters() {
        const now = new Date();
        const currentDay = now.setHours(0, 0, 0, 0);
        const currentHour = now.setMinutes(0, 0, 0);
        const currentMinute = now.setSeconds(0, 0);
        
        this.requestCounts.forEach((counts, key) => {
            if (counts.lastReset.daily < currentDay) {
                counts.daily = 0;
                counts.lastReset.daily = currentDay;
            }
            
            if (counts.lastReset.hourly < currentHour) {
                counts.hourly = 0;
                counts.lastReset.hourly = currentHour;
            }
            
            if (counts.lastReset.minute < currentMinute) {
                counts.minute = 0;
                counts.lastReset.minute = currentMinute;
            }
        });
    }
    
    // Start timer for resetting counters
    startCounterResetTimer() {
        setInterval(() => {
            this.resetCounters();
        }, 60000); // Check every minute
    }
    
    // Make API request with retry logic
    async makeRequest(prompt, options = {}) {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({
                prompt,
                options,
                resolve,
                reject,
                timestamp: Date.now()
            });
            
            this.processQueue();
        });
    }
    
    // Process request queue
    async processQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        while (this.requestQueue.length > 0) {
            const request = this.requestQueue.shift();
            
            try {
                const result = await this.executeRequest(request.prompt, request.options);
                request.resolve(result);
            } catch (error) {
                request.reject(error);
            }
            
            // Rate limiting delay
            await this.rateLimiter.waitIfNeeded();
        }
        
        this.isProcessingQueue = false;
    }
    
    // Execute single API request
    async executeRequest(prompt, options = {}) {
        const availableKey = this.findAvailableApiKey();
        
        if (!availableKey) {
            throw new Error('جميع مفاتيح API وصلت للحد الأقصى. يرجى المحاولة لاحقاً.');
        }
        
        const requestData = {
            contents: [{
                parts: [{ text: prompt }]
            }],
            generationConfig: {
                temperature: options.temperature || 0.7,
                topK: options.topK || 40,
                topP: options.topP || 0.95,
                maxOutputTokens: options.maxOutputTokens || 8192,
            }
        };
        
        try {
            const response = await this.retryManager.executeWithRetry(async () => {
                const result = await fetch(`${CONFIG.GEMINI_API_URL}?key=${availableKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!result.ok) {
                    const errorData = await result.json().catch(() => ({}));
                    throw new Error(`API Error: ${result.status} - ${errorData.error?.message || result.statusText}`);
                }
                
                return result.json();
            });
            
            this.updateRequestCount();
            
            if (response.candidates && response.candidates.length > 0) {
                return response.candidates[0].content.parts[0].text;
            } else {
                throw new Error('لم يتم الحصول على استجابة صالحة من API');
            }
            
        } catch (error) {
            console.error('خطأ في API:', error);
            
            // If rate limited, try next key
            if (error.message.includes('429') || error.message.includes('quota')) {
                this.rotateApiKey();
                throw new Error('تم الوصول لحد الاستخدام، جاري المحاولة مع مفتاح آخر...');
            }
            
            throw error;
        }
    }
    
    // Get API usage statistics
    getUsageStats() {
        const stats = {
            totalKeys: this.apiKeys.length,
            currentKeyIndex: this.currentKeyIndex,
            keyStats: []
        };
        
        this.requestCounts.forEach((counts, key) => {
            stats.keyStats.push({
                key: key.substring(0, 10) + '...',
                daily: counts.daily,
                hourly: counts.hourly,
                minute: counts.minute,
                dailyLimit: CONFIG.MAX_REQUESTS_PER_DAY,
                minuteLimit: CONFIG.MAX_REQUESTS_PER_MINUTE
            });
        });
        
        return stats;
    }
}

// Rate Limiter Class
class RateLimiter {
    constructor() {
        this.requests = [];
        this.maxRequests = CONFIG.RATE_LIMIT.requestsPerSecond;
        this.windowSize = 1000; // 1 second
    }
    
    async waitIfNeeded() {
        const now = Date.now();
        
        // Remove old requests outside the window
        this.requests = this.requests.filter(time => now - time < this.windowSize);
        
        if (this.requests.length >= this.maxRequests) {
            const oldestRequest = Math.min(...this.requests);
            const waitTime = this.windowSize - (now - oldestRequest);
            
            if (waitTime > 0) {
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }
        
        this.requests.push(now);
    }
}

// Retry Manager Class
class RetryManager {
    constructor() {
        this.config = CONFIG.RETRY_CONFIG;
    }
    
    async executeWithRetry(operation) {
        let lastError;
        
        for (let attempt = 1; attempt <= this.config.maxAttempts; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                
                if (attempt === this.config.maxAttempts) {
                    break;
                }
                
                const delay = Math.min(
                    this.config.baseDelay * Math.pow(this.config.backoffFactor, attempt - 1),
                    this.config.maxDelay
                );
                
                console.log(`المحاولة ${attempt} فشلت، إعادة المحاولة خلال ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        
        throw lastError;
    }
}

// Initialize API Manager
const apiManager = new APIManager();

// Register module as loaded
if (typeof window !== 'undefined' && window.loadingManager) {
    window.loadingManager.markLoaded('APIManager');
}
