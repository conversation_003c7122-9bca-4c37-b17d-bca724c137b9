// Main Application Script

class FileAnalyzerApp {
    constructor() {
        this.uploadedFiles = [];
        this.processedResults = [];
        this.isProcessing = false;
        this.isPaused = false;
        this.currentFileIndex = 0;
        this.processingStartTime = null;
        
        this.initializeApp();
    }
    
    // Initialize the application
    initializeApp() {
        this.setupEventListeners();
        this.initializePresetInstructions();
        this.applyInitialSettings();
        this.showWelcomeMessage();
    }
    
    // Setup event listeners
    setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }
        
        if (uploadArea) {
            uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
            uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            uploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));
            uploadArea.addEventListener('click', () => fileInput?.click());
        }
        
        // Processing controls
        const startBtn = document.getElementById('startProcessing');
        const pauseBtn = document.getElementById('pauseProcessing');
        const stopBtn = document.getElementById('stopProcessing');
        
        if (startBtn) startBtn.addEventListener('click', () => this.startProcessing());
        if (pauseBtn) pauseBtn.addEventListener('click', () => this.pauseProcessing());
        if (stopBtn) stopBtn.addEventListener('click', () => this.stopProcessing());
        
        // Export buttons
        const exportButtons = {
            'exportHTML': () => exportManager.exportToHTML(),
            'exportExcel': () => exportManager.exportToExcel(),
            'exportPDF': () => exportManager.exportToPDF(),
            'exportTXT': () => exportManager.exportToTXT(),
            'exportZIP': () => exportManager.exportToZIP()
        };
        
        Object.entries(exportButtons).forEach(([id, handler]) => {
            const btn = document.getElementById(id);
            if (btn) btn.addEventListener('click', handler);
        });
        
        // Settings buttons
        const importSettingsBtn = document.getElementById('importSettings');
        const exportSettingsBtn = document.getElementById('exportSettings');
        
        if (importSettingsBtn) {
            importSettingsBtn.addEventListener('click', () => {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.json';
                input.onchange = (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        settingsManager.importSettings(file);
                    }
                };
                input.click();
            });
        }
        
        if (exportSettingsBtn) {
            exportSettingsBtn.addEventListener('click', () => settingsManager.exportSettings());
        }
        
        // Search functionality
        const searchInput = document.getElementById('searchResults');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.searchResults(e.target.value));
        }
    }
    
    // Initialize preset instructions UI
    initializePresetInstructions() {
        presetManager.renderPresetUI('presetInstructions');
    }
    
    // Apply initial settings
    applyInitialSettings() {
        settingsManager.applyTheme();
    }
    
    // Show welcome message
    showWelcomeMessage() {
        this.showMessage('مرحباً بك في أداة تحليل وتعديل الملفات بالذكاء الصناعي', 'info');
    }
    
    // Handle file selection
    handleFileSelect(event) {
        const files = Array.from(event.target.files);
        this.addFiles(files);
    }
    
    // Handle drag over
    handleDragOver(event) {
        event.preventDefault();
        event.currentTarget.classList.add('dragover');
    }
    
    // Handle drag leave
    handleDragLeave(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');
    }
    
    // Handle file drop
    handleFileDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');
        
        const files = Array.from(event.dataTransfer.files);
        this.addFiles(files);
    }
    
    // Add files to the upload list
    addFiles(files) {
        const validFiles = files.filter(file => {
            const isSupported = ConfigUtils.isFileTypeSupported(file.type) || 
                               this.isFileExtensionSupported(file.name);
            
            if (!isSupported) {
                this.showMessage(`نوع الملف غير مدعوم: ${file.name}`, 'warning');
                return false;
            }
            
            return true;
        });
        
        this.uploadedFiles.push(...validFiles);
        this.updateFilesList();
        this.showMessage(`تم إضافة ${validFiles.length} ملف بنجاح`, 'success');
    }
    
    // Check if file extension is supported
    isFileExtensionSupported(filename) {
        const extension = '.' + filename.split('.').pop().toLowerCase();
        return Object.values(CONFIG.SUPPORTED_FILE_TYPES)
            .some(type => type.extension === extension);
    }
    
    // Update files list display
    updateFilesList() {
        const container = document.getElementById('uploadedFiles');
        if (!container) return;
        
        if (this.uploadedFiles.length === 0) {
            container.innerHTML = '';
            return;
        }
        
        let html = '<h6 class="mb-3">الملفات المرفوعة:</h6>';
        
        this.uploadedFiles.forEach((file, index) => {
            const fileType = ConfigUtils.getFileTypeConfig(file.type);
            const icon = fileType ? fileType.icon : ConfigUtils.getFileIcon('.' + file.name.split('.').pop());
            
            html += `
                <div class="file-item">
                    <div class="file-info">
                        <i class="${icon} file-icon"></i>
                        <div class="file-details">
                            <h6>${file.name}</h6>
                            <small>${this.formatFileSize(file.size)} - ${new Date(file.lastModified).toLocaleString('ar-SA')}</small>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-sm btn-outline-danger" onclick="app.removeFile(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }
    
    // Remove file from list
    removeFile(index) {
        this.uploadedFiles.splice(index, 1);
        this.updateFilesList();
        this.showMessage('تم حذف الملف', 'info');
    }
    
    // Start processing files
    async startProcessing() {
        if (this.uploadedFiles.length === 0) {
            this.showMessage(CONFIG.MESSAGES.NO_FILES_SELECTED, 'warning');
            return;
        }
        
        const customInstructions = document.getElementById('customInstructions').value.trim();
        const selectedPresets = presetManager.getSelectedPresets();
        
        if (!customInstructions && selectedPresets.length === 0) {
            this.showMessage(CONFIG.MESSAGES.NO_INSTRUCTIONS, 'warning');
            return;
        }
        
        this.isProcessing = true;
        this.isPaused = false;
        this.currentFileIndex = 0;
        this.processedResults = [];
        this.processingStartTime = Date.now();
        
        this.updateProcessingUI();
        this.showProgressContainer();
        
        try {
            await this.processAllFiles(customInstructions, selectedPresets);
            this.showMessage(CONFIG.MESSAGES.PROCESSING_COMPLETE, 'success');
            this.showResults();
        } catch (error) {
            this.showMessage(CONFIG.MESSAGES.PROCESSING_ERROR + ': ' + error.message, 'error');
        } finally {
            this.isProcessing = false;
            this.updateProcessingUI();
        }
    }
    
    // Process all files
    async processAllFiles(customInstructions, selectedPresets) {
        const totalFiles = this.uploadedFiles.length;
        const maxConcurrent = settingsManager.getSetting('maxConcurrentRequests') || 3;

        // Process files in batches for better performance
        for (let i = 0; i < totalFiles; i += maxConcurrent) {
            if (!this.isProcessing) break;

            const batch = this.uploadedFiles.slice(i, Math.min(i + maxConcurrent, totalFiles));
            const batchPromises = batch.map(async (file, batchIndex) => {
                const fileIndex = i + batchIndex;

                while (this.isPaused) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                if (!this.isProcessing) return null;

                this.currentFileIndex = fileIndex;
                this.updateProgress();

                try {
                    // Check file size and split if necessary
                    const fileContent = await this.readFileForProcessing(file);
                    let result;

                    if (fileContent.length > CONFIG.CHUNK_SIZE) {
                        result = await this.processLargeFile(file, fileContent, customInstructions, selectedPresets);
                    } else {
                        result = await fileProcessor.processFile(file, customInstructions, selectedPresets);
                    }

                    this.showMessage(`تم معالجة الملف: ${file.name}`, 'success');
                    return result;
                } catch (error) {
                    console.error(`خطأ في معالجة الملف ${file.name}:`, error);
                    this.showMessage(`خطأ في معالجة الملف ${file.name}: ${error.message}`, 'error');
                    return null;
                }
            });

            // Wait for current batch to complete
            const batchResults = await Promise.all(batchPromises);
            batchResults.forEach(result => {
                if (result) {
                    this.processedResults.push(result);
                }
            });
        }
    }

    // Read file content for processing
    async readFileForProcessing(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('خطأ في قراءة الملف'));
            reader.readAsText(file, 'UTF-8');
        });
    }

    // Process large files by splitting them
    async processLargeFile(file, content, customInstructions, selectedPresets) {
        const fileType = ConfigUtils.getFileTypeConfig(file.type)?.processor || 'text';
        const chunks = contentSplitter.splitContent(content, fileType);

        this.showMessage(`تقسيم الملف الكبير ${file.name} إلى ${chunks.length} جزء`, 'info');

        const processedChunks = [];

        for (let i = 0; i < chunks.length; i++) {
            if (!this.isProcessing) break;

            while (this.isPaused) {
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            try {
                // Create temporary file object for chunk
                const chunkFile = new File([chunks[i].content], `${file.name}_chunk_${i}`, {
                    type: file.type,
                    lastModified: file.lastModified
                });

                const chunkResult = await fileProcessor.processFile(chunkFile, customInstructions, selectedPresets);
                processedChunks.push({
                    ...chunkResult,
                    index: chunks[i].index,
                    total: chunks[i].total
                });

                // Update progress for chunk processing
                const chunkProgress = ((i + 1) / chunks.length) * 100;
                this.showMessage(`معالجة الجزء ${i + 1}/${chunks.length} من ${file.name} (${Math.round(chunkProgress)}%)`, 'info');

            } catch (error) {
                console.error(`خطأ في معالجة الجزء ${i} من الملف ${file.name}:`, error);
                this.showMessage(`خطأ في معالجة الجزء ${i + 1} من ${file.name}`, 'warning');
            }
        }

        // Merge processed chunks
        const mergedContent = contentSplitter.mergeChunks(processedChunks);

        return {
            originalName: file.name,
            processedContent: mergedContent,
            fileType: ConfigUtils.getFileTypeConfig(file.type),
            size: file.size,
            lastModified: file.lastModified,
            chunksProcessed: processedChunks.length
        };
    }
    
    // Pause processing
    pauseProcessing() {
        this.isPaused = !this.isPaused;
        this.updateProcessingUI();
        
        const message = this.isPaused ? 'تم إيقاف المعالجة مؤقتاً' : 'تم استئناف المعالجة';
        this.showMessage(message, 'info');
    }
    
    // Stop processing
    stopProcessing() {
        this.isProcessing = false;
        this.isPaused = false;
        this.updateProcessingUI();
        this.hideProgressContainer();
        
        this.showMessage('تم إيقاف المعالجة', 'warning');
    }
    
    // Update processing UI
    updateProcessingUI() {
        const startBtn = document.getElementById('startProcessing');
        const pauseBtn = document.getElementById('pauseProcessing');
        const stopBtn = document.getElementById('stopProcessing');
        
        if (startBtn) startBtn.disabled = this.isProcessing;
        if (pauseBtn) {
            pauseBtn.disabled = !this.isProcessing;
            pauseBtn.innerHTML = this.isPaused ? 
                '<i class="fas fa-play me-2"></i>استئناف' : 
                '<i class="fas fa-pause me-2"></i>إيقاف مؤقت';
        }
        if (stopBtn) stopBtn.disabled = !this.isProcessing;
    }
    
    // Show progress container
    showProgressContainer() {
        const container = document.getElementById('progressContainer');
        if (container) {
            container.style.display = 'block';
        }
    }
    
    // Hide progress container
    hideProgressContainer() {
        const container = document.getElementById('progressContainer');
        if (container) {
            container.style.display = 'none';
        }
    }
    
    // Update progress
    updateProgress() {
        const totalFiles = this.uploadedFiles.length;
        const progress = ((this.currentFileIndex + 1) / totalFiles) * 100;
        
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const progressPercentage = document.getElementById('progressPercentage');
        const processingDetails = document.getElementById('processingDetails');
        
        if (progressBar) progressBar.style.width = progress + '%';
        if (progressPercentage) progressPercentage.textContent = Math.round(progress) + '%';
        
        if (progressText) {
            const currentFile = this.uploadedFiles[this.currentFileIndex];
            progressText.textContent = `معالجة: ${currentFile?.name || 'غير محدد'}`;
        }
        
        if (processingDetails) {
            const remaining = totalFiles - this.currentFileIndex - 1;
            const elapsed = Date.now() - this.processingStartTime;
            const estimatedTotal = (elapsed / (this.currentFileIndex + 1)) * totalFiles;
            const estimatedRemaining = estimatedTotal - elapsed;
            
            processingDetails.innerHTML = `
                الملف الحالي: ${this.currentFileIndex + 1}/${totalFiles} | 
                المتبقي: ${remaining} | 
                الوقت المقدر: ${this.formatTime(estimatedRemaining)}
            `;
        }
    }
    
    // Show results
    showResults() {
        const resultsSection = document.getElementById('resultsSection');
        const exportSection = document.getElementById('exportSection');
        const resultsContent = document.getElementById('resultsContent');
        
        if (resultsSection) resultsSection.style.display = 'block';
        if (exportSection) exportSection.style.display = 'block';
        
        if (resultsContent && this.processedResults.length > 0) {
            let html = '';
            
            this.processedResults.forEach((result, index) => {
                html += `
                    <div class="result-item" data-index="${index}">
                        <div class="result-header">
                            <h6 class="result-title">${result.originalName}</h6>
                            <small class="text-muted">${result.fileType?.extension || 'غير محدد'}</small>
                        </div>
                        <div class="result-content">${this.escapeHtml(result.processedContent || result.content || '')}</div>
                    </div>
                `;
            });
            
            resultsContent.innerHTML = html;
            
            // Set results for export
            exportManager.setResults(this.processedResults);
        }
    }
    
    // Search results
    searchResults(query) {
        const resultItems = document.querySelectorAll('.result-item');
        const searchTerm = query.toLowerCase();
        
        resultItems.forEach(item => {
            const content = item.textContent.toLowerCase();
            if (content.includes(searchTerm) || !searchTerm) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }
    
    // Utility functions
    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    formatTime(milliseconds) {
        if (milliseconds < 0) return '0 ثانية';
        
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours} ساعة ${minutes % 60} دقيقة`;
        } else if (minutes > 0) {
            return `${minutes} دقيقة ${seconds % 60} ثانية`;
        } else {
            return `${seconds} ثانية`;
        }
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    showMessage(message, type) {
        // Check if notifications are enabled
        if (!settingsManager.getSetting('enableNotifications')) {
            return;
        }

        // Create notification container if it doesn't exist
        let container = document.querySelector('.notification-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'notification-container';
            document.body.appendChild(container);
        }

        const notification = document.createElement('div');
        notification.className = `alert alert-${this.getAlertClass(type)} notification fade-in`;
        notification.innerHTML = `
            <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
            <span class="notification-message">${message}</span>
            <button type="button" class="btn-close" onclick="this.removeNotification(this.parentElement)"></button>
        `;

        container.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            this.removeNotification(notification);
        }, 5000);

        // Limit number of notifications
        const notifications = container.querySelectorAll('.notification');
        if (notifications.length > 5) {
            this.removeNotification(notifications[0]);
        }
    }

    removeNotification(notification) {
        if (notification && notification.parentElement) {
            notification.classList.add('removing');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }
    }
    
    getAlertClass(type) {
        const classes = {
            'success': 'success',
            'error': 'danger',
            'warning': 'warning',
            'info': 'info'
        };
        return classes[type] || 'info';
    }
    
    getAlertIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// Global error handler
window.addEventListener('error', (event) => {
    console.error('خطأ عام في التطبيق:', event.error);
    if (window.app) {
        window.app.showMessage('حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة.', 'error');
    }
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    console.error('خطأ في Promise:', event.reason);
    if (window.app) {
        window.app.showMessage('حدث خطأ في المعالجة. يرجى المحاولة مرة أخرى.', 'error');
    }
    event.preventDefault();
});

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait for all modules to load
    if (typeof window.loadingManager !== 'undefined') {
        window.loadingManager.onReady(initializeApp);
    } else {
        // Fallback - try to initialize after a short delay
        setTimeout(initializeApp, 1000);
    }
});

function initializeApp() {
    try {
        // Check for required dependencies
        const requiredGlobals = ['CONFIG', 'apiManager', 'fileProcessor', 'contentSplitter',
                               'presetManager', 'exportManager', 'settingsManager'];

        const missingDependencies = requiredGlobals.filter(dep => typeof window[dep] === 'undefined');

        if (missingDependencies.length > 0) {
            console.error('مكونات مفقودة:', missingDependencies);
            showErrorPage('خطأ في تحميل التطبيق',
                         'فشل في تحميل بعض المكونات المطلوبة:',
                         missingDependencies);
            return;
        }

        // Initialize the application
        window.app = new FileAnalyzerApp();

        // Add keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            // Ctrl+O or Cmd+O to open file dialog
            if ((event.ctrlKey || event.metaKey) && event.key === 'o') {
                event.preventDefault();
                const fileInput = document.getElementById('fileInput');
                if (fileInput) fileInput.click();
            }

            // Ctrl+S or Cmd+S to export (prevent default save)
            if ((event.ctrlKey || event.metaKey) && event.key === 's') {
                event.preventDefault();
                if (window.app && window.app.processedResults.length > 0) {
                    exportManager.exportToHTML();
                }
            }

            // Escape to stop processing
            if (event.key === 'Escape' && window.app && window.app.isProcessing) {
                window.app.stopProcessing();
            }
        });

        console.log('تم تحميل أداة تحليل وتعديل الملفات بنجاح');

    } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error);
        showErrorPage('خطأ في تهيئة التطبيق', error.message);
    }
}

function showErrorPage(title, message, list = null) {
    const listHtml = list ? `<ul style="color: #991b1b;">${list.map(item => `<li>${item}</li>`).join('')}</ul>` : '';

    document.body.innerHTML = `
        <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif; direction: rtl; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #dc2626;">${title}</h2>
            <p style="color: #991b1b; margin: 20px 0;">${message}</p>
            ${listHtml}
            <div style="margin-top: 30px;">
                <button onclick="location.reload()" style="background: #2563eb; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 5px; font-size: 14px;">
                    إعادة تحميل الصفحة
                </button>
                <button onclick="window.open('test.html', '_blank')" style="background: #059669; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 5px; font-size: 14px;">
                    فتح صفحة الاختبار
                </button>
            </div>
            <div style="margin-top: 20px; padding: 15px; background: #f3f4f6; border-radius: 6px; text-align: right;">
                <h4 style="color: #374151; margin-bottom: 10px;">نصائح لحل المشكلة:</h4>
                <ul style="color: #6b7280; text-align: right;">
                    <li>تأكد من اتصالك بالإنترنت</li>
                    <li>امسح ذاكرة التخزين المؤقت للمتصفح</li>
                    <li>جرب متصفح آخر</li>
                    <li>تأكد من تفعيل JavaScript</li>
                </ul>
            </div>
        </div>
    `;
}
