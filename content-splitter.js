// Content Splitter for handling large files and avoiding token limits

class ContentSplitter {
    constructor() {
        this.chunkSize = CONFIG.CHUNK_SIZE;
        this.overlapSize = CONFIG.OVERLAP_SIZE;
        this.maxTokensPerRequest = CONFIG.MAX_TOKENS_PER_REQUEST;
    }
    
    // Main method to split content intelligently
    splitContent(content, fileType = 'text') {
        if (this.isContentSmall(content)) {
            return [{
                content: content,
                index: 0,
                total: 1,
                isComplete: true
            }];
        }
        
        // Choose splitting strategy based on file type
        switch (fileType) {
            case 'html':
                return this.splitHtmlContent(content);
            case 'markdown':
                return this.splitMarkdownContent(content);
            case 'javascript':
            case 'css':
                return this.splitCodeContent(content);
            case 'json':
                return this.splitJsonContent(content);
            case 'xml':
                return this.splitXmlContent(content);
            default:
                return this.splitTextContent(content);
        }
    }
    
    // Check if content is small enough to process in one request
    isContentSmall(content) {
        return content.length <= this.chunkSize;
    }
    
    // Split plain text content
    splitTextContent(content) {
        const chunks = [];
        const lines = content.split('\n');
        let currentChunk = '';
        let chunkIndex = 0;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const potentialChunk = currentChunk + (currentChunk ? '\n' : '') + line;
            
            if (potentialChunk.length > this.chunkSize && currentChunk) {
                // Save current chunk
                chunks.push({
                    content: currentChunk,
                    index: chunkIndex,
                    startLine: this.getStartLine(chunks, chunkIndex),
                    endLine: this.getStartLine(chunks, chunkIndex) + currentChunk.split('\n').length - 1
                });
                
                // Start new chunk with overlap
                const overlapLines = this.getOverlapLines(currentChunk);
                currentChunk = overlapLines + line;
                chunkIndex++;
            } else {
                currentChunk = potentialChunk;
            }
        }
        
        // Add the last chunk
        if (currentChunk) {
            chunks.push({
                content: currentChunk,
                index: chunkIndex,
                startLine: this.getStartLine(chunks, chunkIndex),
                endLine: this.getStartLine(chunks, chunkIndex) + currentChunk.split('\n').length - 1
            });
        }
        
        // Add metadata to all chunks
        return chunks.map(chunk => ({
            ...chunk,
            total: chunks.length,
            isComplete: chunks.length === 1
        }));
    }
    
    // Split HTML content preserving structure
    splitHtmlContent(content) {
        // Try to split at major HTML elements
        const majorElements = ['</body>', '</div>', '</section>', '</article>', '</main>', '</header>', '</footer>'];
        
        for (const element of majorElements) {
            const parts = content.split(element);
            if (parts.length > 1) {
                const chunks = [];
                let currentChunk = '';
                let chunkIndex = 0;
                
                for (let i = 0; i < parts.length; i++) {
                    const part = parts[i] + (i < parts.length - 1 ? element : '');
                    const potentialChunk = currentChunk + part;
                    
                    if (potentialChunk.length > this.chunkSize && currentChunk) {
                        chunks.push({
                            content: this.ensureHtmlValidity(currentChunk),
                            index: chunkIndex,
                            type: 'html-structure'
                        });
                        
                        currentChunk = this.getHtmlOverlap(currentChunk) + part;
                        chunkIndex++;
                    } else {
                        currentChunk = potentialChunk;
                    }
                }
                
                if (currentChunk) {
                    chunks.push({
                        content: this.ensureHtmlValidity(currentChunk),
                        index: chunkIndex,
                        type: 'html-structure'
                    });
                }
                
                if (chunks.length > 1) {
                    return chunks.map(chunk => ({
                        ...chunk,
                        total: chunks.length,
                        isComplete: false
                    }));
                }
            }
        }
        
        // Fallback to text splitting
        return this.splitTextContent(content);
    }
    
    // Split Markdown content preserving structure
    splitMarkdownContent(content) {
        // Split at headers
        const headerRegex = /^#{1,6}\s+.+$/gm;
        const sections = [];
        let lastIndex = 0;
        let match;
        
        while ((match = headerRegex.exec(content)) !== null) {
            if (lastIndex < match.index) {
                sections.push(content.substring(lastIndex, match.index));
            }
            lastIndex = match.index;
        }
        
        if (lastIndex < content.length) {
            sections.push(content.substring(lastIndex));
        }
        
        // Group sections into chunks
        const chunks = [];
        let currentChunk = '';
        let chunkIndex = 0;
        
        for (const section of sections) {
            const potentialChunk = currentChunk + section;
            
            if (potentialChunk.length > this.chunkSize && currentChunk) {
                chunks.push({
                    content: currentChunk,
                    index: chunkIndex,
                    type: 'markdown-section'
                });
                
                currentChunk = section;
                chunkIndex++;
            } else {
                currentChunk = potentialChunk;
            }
        }
        
        if (currentChunk) {
            chunks.push({
                content: currentChunk,
                index: chunkIndex,
                type: 'markdown-section'
            });
        }
        
        return chunks.map(chunk => ({
            ...chunk,
            total: chunks.length,
            isComplete: chunks.length === 1
        }));
    }
    
    // Split code content preserving functions/blocks
    splitCodeContent(content) {
        // Try to split at function boundaries
        const functionRegex = /^(function\s+\w+|const\s+\w+\s*=|let\s+\w+\s*=|var\s+\w+\s*=|\w+\s*:\s*function)/gm;
        
        return this.splitAtRegexBoundaries(content, functionRegex, 'code-function');
    }
    
    // Split JSON content preserving structure
    splitJsonContent(content) {
        try {
            const parsed = JSON.parse(content);
            
            if (Array.isArray(parsed)) {
                return this.splitJsonArray(parsed);
            } else if (typeof parsed === 'object') {
                return this.splitJsonObject(parsed);
            }
        } catch (e) {
            // If not valid JSON, treat as text
            return this.splitTextContent(content);
        }
        
        return [{
            content: content,
            index: 0,
            total: 1,
            isComplete: true
        }];
    }
    
    // Split XML content preserving structure
    splitXmlContent(content) {
        const elementRegex = /<\/\w+>/g;
        return this.splitAtRegexBoundaries(content, elementRegex, 'xml-element');
    }
    
    // Helper method to split at regex boundaries
    splitAtRegexBoundaries(content, regex, type) {
        const matches = [];
        let match;
        
        while ((match = regex.exec(content)) !== null) {
            matches.push(match.index);
        }
        
        if (matches.length === 0) {
            return this.splitTextContent(content);
        }
        
        const chunks = [];
        let currentChunk = '';
        let chunkIndex = 0;
        let lastIndex = 0;
        
        for (const matchIndex of matches) {
            const segment = content.substring(lastIndex, matchIndex);
            const potentialChunk = currentChunk + segment;
            
            if (potentialChunk.length > this.chunkSize && currentChunk) {
                chunks.push({
                    content: currentChunk,
                    index: chunkIndex,
                    type: type
                });
                
                currentChunk = this.getCodeOverlap(currentChunk) + segment;
                chunkIndex++;
            } else {
                currentChunk = potentialChunk;
            }
            
            lastIndex = matchIndex;
        }
        
        // Add remaining content
        if (lastIndex < content.length) {
            currentChunk += content.substring(lastIndex);
        }
        
        if (currentChunk) {
            chunks.push({
                content: currentChunk,
                index: chunkIndex,
                type: type
            });
        }
        
        return chunks.map(chunk => ({
            ...chunk,
            total: chunks.length,
            isComplete: chunks.length === 1
        }));
    }
    
    // Split JSON array
    splitJsonArray(array) {
        const chunks = [];
        let currentItems = [];
        let chunkIndex = 0;
        
        for (const item of array) {
            const itemStr = JSON.stringify(item, null, 2);
            const currentStr = JSON.stringify(currentItems, null, 2);
            
            if ((currentStr + itemStr).length > this.chunkSize && currentItems.length > 0) {
                chunks.push({
                    content: JSON.stringify(currentItems, null, 2),
                    index: chunkIndex,
                    type: 'json-array',
                    itemCount: currentItems.length
                });
                
                currentItems = [item];
                chunkIndex++;
            } else {
                currentItems.push(item);
            }
        }
        
        if (currentItems.length > 0) {
            chunks.push({
                content: JSON.stringify(currentItems, null, 2),
                index: chunkIndex,
                type: 'json-array',
                itemCount: currentItems.length
            });
        }
        
        return chunks.map(chunk => ({
            ...chunk,
            total: chunks.length,
            isComplete: chunks.length === 1
        }));
    }
    
    // Split JSON object
    splitJsonObject(obj) {
        const keys = Object.keys(obj);
        const chunks = [];
        let currentObj = {};
        let chunkIndex = 0;
        
        for (const key of keys) {
            const value = obj[key];
            const tempObj = { ...currentObj, [key]: value };
            const tempStr = JSON.stringify(tempObj, null, 2);
            
            if (tempStr.length > this.chunkSize && Object.keys(currentObj).length > 0) {
                chunks.push({
                    content: JSON.stringify(currentObj, null, 2),
                    index: chunkIndex,
                    type: 'json-object',
                    keyCount: Object.keys(currentObj).length
                });
                
                currentObj = { [key]: value };
                chunkIndex++;
            } else {
                currentObj[key] = value;
            }
        }
        
        if (Object.keys(currentObj).length > 0) {
            chunks.push({
                content: JSON.stringify(currentObj, null, 2),
                index: chunkIndex,
                type: 'json-object',
                keyCount: Object.keys(currentObj).length
            });
        }
        
        return chunks.map(chunk => ({
            ...chunk,
            total: chunks.length,
            isComplete: chunks.length === 1
        }));
    }
    
    // Merge processed chunks back together
    mergeChunks(processedChunks) {
        if (processedChunks.length === 1) {
            return processedChunks[0].content;
        }
        
        // Sort chunks by index
        const sortedChunks = processedChunks.sort((a, b) => a.index - b.index);
        
        // Remove overlap and merge
        let mergedContent = sortedChunks[0].content;
        
        for (let i = 1; i < sortedChunks.length; i++) {
            const currentChunk = sortedChunks[i];
            const cleanedContent = this.removeOverlap(mergedContent, currentChunk.content);
            mergedContent += cleanedContent;
        }
        
        return mergedContent;
    }
    
    // Helper methods
    getStartLine(chunks, index) {
        if (index === 0) return 1;
        return chunks[index - 1].endLine + 1;
    }
    
    getOverlapLines(content) {
        const lines = content.split('\n');
        const overlapLineCount = Math.min(5, Math.floor(this.overlapSize / 100));
        return lines.slice(-overlapLineCount).join('\n') + '\n';
    }
    
    getHtmlOverlap(content) {
        // Get last few HTML elements for context
        const lastElements = content.match(/<[^>]+>/g);
        if (lastElements && lastElements.length > 0) {
            return lastElements.slice(-3).join('');
        }
        return '';
    }
    
    getCodeOverlap(content) {
        const lines = content.split('\n');
        return lines.slice(-2).join('\n') + '\n';
    }
    
    ensureHtmlValidity(htmlContent) {
        // Basic HTML validation - ensure tags are closed
        // This is a simplified version
        return htmlContent;
    }
    
    removeOverlap(existingContent, newContent) {
        // Simple overlap removal - in practice, this would be more sophisticated
        const overlapSize = Math.min(this.overlapSize, newContent.length / 2);
        const potentialOverlap = existingContent.slice(-overlapSize);
        
        if (newContent.startsWith(potentialOverlap)) {
            return newContent.slice(overlapSize);
        }
        
        return newContent;
    }
}

// Initialize Content Splitter
const contentSplitter = new ContentSplitter();

// Register module as loaded
if (typeof window !== 'undefined' && window.loadingManager) {
    window.loadingManager.markLoaded('ContentSplitter');
}
