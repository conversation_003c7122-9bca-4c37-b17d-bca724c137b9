// Preset Instructions Manager

class PresetInstructionsManager {
    constructor() {
        this.presetInstructions = this.initializePresets();
        this.selectedPresets = new Set();
    }
    
    // Initialize preset instructions
    initializePresets() {
        return {
            // SEO Improvements
            'improve-seo': {
                name: 'تحسين السيو',
                description: 'تحسين محركات البحث للملفات HTML',
                category: 'seo',
                icon: 'fas fa-search',
                applicable: ['html', 'markdown'],
                instruction: 'قم بتحسين السيو للمحتوى من خلال إضافة meta tags مناسبة، تحسين العناوين، إضافة alt text للصور، وتحسين البنية الدلالية'
            },
            
            // Formatting Improvements
            'improve-formatting': {
                name: 'تحسين التنسيق',
                description: 'تحسين تنسيق الكود والمحتوى',
                category: 'formatting',
                icon: 'fas fa-align-left',
                applicable: ['html', 'css', 'javascript', 'json', 'xml'],
                instruction: 'قم بتحسين تنسيق الكود وجعله أكثر قابلية للقراءة مع الحفاظ على الوظائف'
            },
            
            'remove-html-codes': {
                name: 'إزالة أكواد HTML',
                description: 'إزالة أكواد HTML من المحتوى',
                category: 'cleanup',
                icon: 'fas fa-code',
                applicable: ['text', 'markdown'],
                instruction: 'قم بإزالة جميع أكواد HTML والعناصر التي تظهر في واجهة الصفحة'
            },
            
            'remove-links': {
                name: 'حذف الروابط',
                description: 'إزالة جميع الروابط من المحتوى',
                category: 'cleanup',
                icon: 'fas fa-unlink',
                applicable: ['text', 'html', 'markdown'],
                instruction: 'قم بإزالة جميع الروابط HTTP وHTTPS من المحتوى'
            },
            
            'remove-duplicates': {
                name: 'حذف التكرار',
                description: 'إزالة الأسطر والمحتوى المكرر',
                category: 'cleanup',
                icon: 'fas fa-copy',
                applicable: ['text', 'csv'],
                instruction: 'قم بإزالة الأسطر المكررة والمحتوى المتكرر'
            },
            
            'remove-empty-lines': {
                name: 'حذف الأسطر الفارغة',
                description: 'إزالة الأسطر الفارغة الزائدة',
                category: 'cleanup',
                icon: 'fas fa-minus',
                applicable: ['text', 'html', 'css', 'javascript'],
                instruction: 'قم بإزالة الأسطر الفارغة الزائدة مع الحفاظ على التنسيق'
            },
            
            'compress-code': {
                name: 'ضغط الكود',
                description: 'ضغط الكود وتقليل حجمه',
                category: 'optimization',
                icon: 'fas fa-compress',
                applicable: ['css', 'javascript', 'html'],
                instruction: 'قم بضغط الكود وإزالة المسافات الزائدة والتعليقات غير الضرورية'
            },
            
            // Batch File Specific
            'create-profile-variants': {
                name: 'إنشاء متغيرات البروفايل',
                description: 'إنشاء عدة نسخ من ملف BAT بمتغيرات مختلفة للبروفايل',
                category: 'batch',
                icon: 'fas fa-users',
                applicable: ['batch'],
                instruction: 'قم بإنشاء عدة نسخ من الملف مع تغيير رقم البروفايل من Profile 6 إلى Profile 7, 8, 9, 10',
                isDefault: true
            },
            
            'modify-paths': {
                name: 'تعديل المسارات',
                description: 'تعديل مسارات الملفات في ملفات BAT',
                category: 'batch',
                icon: 'fas fa-folder-open',
                applicable: ['batch'],
                instruction: 'قم بتعديل مسارات الملفات والبرامج في ملف BAT'
            },
            
            // Content Enhancement
            'enhance-readability': {
                name: 'تحسين القابلية للقراءة',
                description: 'تحسين قابلية المحتوى للقراءة',
                category: 'enhancement',
                icon: 'fas fa-eye',
                applicable: ['text', 'markdown', 'html'],
                instruction: 'قم بتحسين قابلية المحتوى للقراءة من خلال تنظيم الفقرات وتحسين التدفق'
            },
            
            'add-comments': {
                name: 'إضافة تعليقات',
                description: 'إضافة تعليقات توضيحية للكود',
                category: 'documentation',
                icon: 'fas fa-comment',
                applicable: ['javascript', 'css', 'batch'],
                instruction: 'قم بإضافة تعليقات توضيحية مفيدة للكود'
            },
            
            'standardize-naming': {
                name: 'توحيد التسمية',
                description: 'توحيد أسماء المتغيرات والدوال',
                category: 'standardization',
                icon: 'fas fa-tags',
                applicable: ['javascript', 'css'],
                instruction: 'قم بتوحيد أسماء المتغيرات والدوال وفقاً لمعايير التسمية الجيدة'
            },
            
            // Security
            'remove-sensitive-data': {
                name: 'إزالة البيانات الحساسة',
                description: 'إزالة كلمات المرور والبيانات الحساسة',
                category: 'security',
                icon: 'fas fa-shield-alt',
                applicable: ['text', 'javascript', 'json'],
                instruction: 'قم بإزالة أو إخفاء كلمات المرور والبيانات الحساسة'
            },
            
            // Language Processing
            'fix-arabic-text': {
                name: 'إصلاح النص العربي',
                description: 'إصلاح مشاكل النص العربي والترقيم',
                category: 'language',
                icon: 'fas fa-language',
                applicable: ['text', 'html', 'markdown'],
                instruction: 'قم بإصلاح مشاكل النص العربي والترقيم وتحسين التنسيق'
            },
            
            'translate-comments': {
                name: 'ترجمة التعليقات',
                description: 'ترجمة التعليقات إلى العربية',
                category: 'language',
                icon: 'fas fa-globe',
                applicable: ['javascript', 'css', 'html'],
                instruction: 'قم بترجمة التعليقات الموجودة في الكود إلى اللغة العربية'
            }
        };
    }
    
    // Get presets by category
    getPresetsByCategory() {
        const categories = {};
        
        Object.entries(this.presetInstructions).forEach(([key, preset]) => {
            if (!categories[preset.category]) {
                categories[preset.category] = [];
            }
            categories[preset.category].push({ key, ...preset });
        });
        
        return categories;
    }
    
    // Get applicable presets for file type
    getApplicablePresets(fileType) {
        return Object.entries(this.presetInstructions)
            .filter(([key, preset]) => preset.applicable.includes(fileType))
            .map(([key, preset]) => ({ key, ...preset }));
    }
    
    // Get category display names
    getCategoryNames() {
        return {
            'seo': 'تحسين محركات البحث',
            'formatting': 'التنسيق',
            'cleanup': 'التنظيف',
            'optimization': 'التحسين',
            'batch': 'ملفات BAT',
            'enhancement': 'التحسين',
            'documentation': 'التوثيق',
            'standardization': 'التوحيد القياسي',
            'security': 'الأمان',
            'language': 'اللغة'
        };
    }
    
    // Toggle preset selection
    togglePreset(presetKey) {
        if (this.selectedPresets.has(presetKey)) {
            this.selectedPresets.delete(presetKey);
        } else {
            this.selectedPresets.add(presetKey);
        }
    }
    
    // Get selected presets
    getSelectedPresets() {
        return Array.from(this.selectedPresets);
    }
    
    // Clear all selections
    clearSelections() {
        this.selectedPresets.clear();
    }
    
    // Select default presets for file type
    selectDefaultPresets(fileType) {
        this.clearSelections();
        
        Object.entries(this.presetInstructions).forEach(([key, preset]) => {
            if (preset.applicable.includes(fileType) && preset.isDefault) {
                this.selectedPresets.add(key);
            }
        });
    }
    
    // Get preset instruction text
    getPresetInstruction(presetKey) {
        return this.presetInstructions[presetKey]?.instruction || '';
    }
    
    // Get combined instructions for selected presets
    getCombinedInstructions() {
        const instructions = [];
        
        this.selectedPresets.forEach(presetKey => {
            const preset = this.presetInstructions[presetKey];
            if (preset) {
                instructions.push(`${preset.name}: ${preset.instruction}`);
            }
        });
        
        return instructions.join('\n\n');
    }
    
    // Render preset UI
    renderPresetUI(containerId, fileType = null) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const categories = this.getPresetsByCategory();
        const categoryNames = this.getCategoryNames();
        const applicablePresets = fileType ? this.getApplicablePresets(fileType) : null;
        
        let html = '<div class="preset-categories">';
        
        Object.entries(categories).forEach(([categoryKey, presets]) => {
            // Filter presets if file type is specified
            const filteredPresets = applicablePresets ? 
                presets.filter(preset => applicablePresets.some(ap => ap.key === preset.key)) : 
                presets;
            
            if (filteredPresets.length === 0) return;
            
            html += `
                <div class="preset-category mb-3">
                    <h6 class="category-title text-primary mb-2">
                        <i class="fas fa-folder me-2"></i>
                        ${categoryNames[categoryKey] || categoryKey}
                    </h6>
                    <div class="preset-items">
            `;
            
            filteredPresets.forEach(preset => {
                const isSelected = this.selectedPresets.has(preset.key);
                const isDefault = preset.isDefault ? ' (افتراضي)' : '';
                
                html += `
                    <div class="preset-item d-flex align-items-center mb-2">
                        <input type="checkbox" 
                               id="preset-${preset.key}" 
                               class="form-check-input me-2" 
                               ${isSelected ? 'checked' : ''}
                               onchange="presetManager.togglePreset('${preset.key}')">
                        <label for="preset-${preset.key}" class="form-check-label flex-grow-1">
                            <i class="${preset.icon} me-2 text-secondary"></i>
                            <strong>${preset.name}${isDefault}</strong>
                            <br>
                            <small class="text-muted">${preset.description}</small>
                        </label>
                    </div>
                `;
            });
            
            html += `
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        
        // Add control buttons
        html += `
            <div class="preset-controls mt-3 pt-3 border-top">
                <button class="btn btn-sm btn-outline-primary me-2" onclick="presetManager.selectAll('${fileType}')">
                    <i class="fas fa-check-double me-1"></i>
                    تحديد الكل
                </button>
                <button class="btn btn-sm btn-outline-secondary me-2" onclick="presetManager.clearSelections(); presetManager.renderPresetUI('${containerId}', '${fileType}')">
                    <i class="fas fa-times me-1"></i>
                    إلغاء التحديد
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="presetManager.selectDefaultPresets('${fileType}'); presetManager.renderPresetUI('${containerId}', '${fileType}')">
                    <i class="fas fa-star me-1"></i>
                    الافتراضي
                </button>
            </div>
        `;
        
        container.innerHTML = html;
    }
    
    // Select all applicable presets
    selectAll(fileType) {
        if (fileType) {
            const applicable = this.getApplicablePresets(fileType);
            applicable.forEach(preset => {
                this.selectedPresets.add(preset.key);
            });
        } else {
            Object.keys(this.presetInstructions).forEach(key => {
                this.selectedPresets.add(key);
            });
        }
    }
    
    // Export presets configuration
    exportConfiguration() {
        return {
            selectedPresets: Array.from(this.selectedPresets),
            timestamp: new Date().toISOString()
        };
    }
    
    // Import presets configuration
    importConfiguration(config) {
        if (config.selectedPresets && Array.isArray(config.selectedPresets)) {
            this.selectedPresets = new Set(config.selectedPresets);
            return true;
        }
        return false;
    }
}

// Initialize Preset Instructions Manager
const presetManager = new PresetInstructionsManager();
