// Settings Manager for handling application settings and preferences

class SettingsManager {
    constructor() {
        this.settings = { ...CONFIG.DEFAULT_SETTINGS };
        this.settingsKey = 'fileAnalyzerSettings';
        this.loadSettings();
    }
    
    // Load settings from localStorage
    loadSettings() {
        try {
            const savedSettings = localStorage.getItem(this.settingsKey);
            if (savedSettings) {
                const parsed = JSON.parse(savedSettings);
                this.settings = { ...this.settings, ...parsed };
            }
        } catch (error) {
            console.warn('خطأ في تحميل الإعدادات:', error);
        }
    }
    
    // Save settings to localStorage
    saveSettings() {
        try {
            localStorage.setItem(this.settingsKey, JSON.stringify(this.settings));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    }
    
    // Get setting value
    getSetting(key) {
        return this.settings[key];
    }
    
    // Set setting value
    setSetting(key, value) {
        this.settings[key] = value;
        this.saveSettings();
    }
    
    // Get all settings
    getAllSettings() {
        return { ...this.settings };
    }
    
    // Reset to default settings
    resetToDefaults() {
        this.settings = { ...CONFIG.DEFAULT_SETTINGS };
        this.saveSettings();
    }
    
    // Export settings to file
    exportSettings() {
        try {
            const exportData = {
                settings: this.settings,
                presets: presetManager.exportConfiguration(),
                exportDate: new Date().toISOString(),
                version: '1.0'
            };
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json;charset=utf-8'
            });
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `إعدادات_أداة_التحليل_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.showMessage('تم تصدير الإعدادات بنجاح', 'success');
            return true;
        } catch (error) {
            this.showMessage('خطأ في تصدير الإعدادات: ' + error.message, 'error');
            return false;
        }
    }
    
    // Import settings from file
    importSettings(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const importData = JSON.parse(e.target.result);
                    
                    // Validate import data
                    if (!this.validateImportData(importData)) {
                        throw new Error('ملف الإعدادات غير صالح');
                    }
                    
                    // Import settings
                    if (importData.settings) {
                        this.settings = { ...CONFIG.DEFAULT_SETTINGS, ...importData.settings };
                        this.saveSettings();
                    }
                    
                    // Import presets
                    if (importData.presets) {
                        presetManager.importConfiguration(importData.presets);
                    }
                    
                    this.showMessage('تم استيراد الإعدادات بنجاح', 'success');
                    resolve(true);
                } catch (error) {
                    this.showMessage('خطأ في استيراد الإعدادات: ' + error.message, 'error');
                    reject(error);
                }
            };
            
            reader.onerror = () => {
                const error = new Error('خطأ في قراءة ملف الإعدادات');
                this.showMessage(error.message, 'error');
                reject(error);
            };
            
            reader.readAsText(file, 'UTF-8');
        });
    }
    
    // Validate import data
    validateImportData(data) {
        if (!data || typeof data !== 'object') {
            return false;
        }
        
        // Check if it has the expected structure
        if (!data.settings && !data.presets) {
            return false;
        }
        
        // Validate settings structure
        if (data.settings && typeof data.settings !== 'object') {
            return false;
        }
        
        return true;
    }
    
    // Create settings UI
    createSettingsUI() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'settingsModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-cog me-2"></i>
                            إعدادات التطبيق
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">الإعدادات العامة</h6>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoSave" 
                                               ${this.settings.autoSave ? 'checked' : ''}>
                                        <label class="form-check-label" for="autoSave">
                                            الحفظ التلقائي
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="showProgress" 
                                               ${this.settings.showProgress ? 'checked' : ''}>
                                        <label class="form-check-label" for="showProgress">
                                            إظهار شريط التقدم
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableNotifications" 
                                               ${this.settings.enableNotifications ? 'checked' : ''}>
                                        <label class="form-check-label" for="enableNotifications">
                                            تفعيل الإشعارات
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="maxConcurrentRequests" class="form-label">
                                        عدد الطلبات المتزامنة
                                    </label>
                                    <input type="range" class="form-range" id="maxConcurrentRequests" 
                                           min="1" max="10" value="${this.settings.maxConcurrentRequests}">
                                    <div class="d-flex justify-content-between">
                                        <small>1</small>
                                        <small id="concurrentValue">${this.settings.maxConcurrentRequests}</small>
                                        <small>10</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">إعدادات المعالجة</h6>
                                
                                <div class="mb-3">
                                    <label for="retryAttempts" class="form-label">
                                        عدد محاولات الإعادة
                                    </label>
                                    <input type="number" class="form-control" id="retryAttempts" 
                                           min="1" max="10" value="${this.settings.retryAttempts}">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="retryDelay" class="form-label">
                                        تأخير الإعادة (مللي ثانية)
                                    </label>
                                    <input type="number" class="form-control" id="retryDelay" 
                                           min="1000" max="10000" step="500" value="${this.settings.retryDelay}">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="theme" class="form-label">المظهر</label>
                                    <select class="form-select" id="theme">
                                        <option value="light" ${this.settings.theme === 'light' ? 'selected' : ''}>فاتح</option>
                                        <option value="dark" ${this.settings.theme === 'dark' ? 'selected' : ''}>داكن</option>
                                        <option value="auto" ${this.settings.theme === 'auto' ? 'selected' : ''}>تلقائي</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="language" class="form-label">اللغة</label>
                                    <select class="form-select" id="language">
                                        <option value="ar" ${this.settings.language === 'ar' ? 'selected' : ''}>العربية</option>
                                        <option value="en" ${this.settings.language === 'en' ? 'selected' : ''}>English</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">إدارة الإعدادات</h6>
                                <div class="d-flex gap-2 flex-wrap">
                                    <button class="btn btn-outline-primary" onclick="settingsManager.exportSettings()">
                                        <i class="fas fa-download me-1"></i>
                                        تصدير الإعدادات
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="document.getElementById('importSettingsFile').click()">
                                        <i class="fas fa-upload me-1"></i>
                                        استيراد الإعدادات
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="settingsManager.resetToDefaults(); settingsManager.updateSettingsUI()">
                                        <i class="fas fa-undo me-1"></i>
                                        إعادة تعيين
                                    </button>
                                </div>
                                <input type="file" id="importSettingsFile" accept=".json" style="display: none;" 
                                       onchange="settingsManager.handleImportFile(this)">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="settingsManager.saveSettingsFromUI()">
                            حفظ الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Add event listeners
        this.addSettingsEventListeners();
        
        return modal;
    }
    
    // Add event listeners for settings UI
    addSettingsEventListeners() {
        // Range slider for concurrent requests
        const rangeInput = document.getElementById('maxConcurrentRequests');
        const rangeValue = document.getElementById('concurrentValue');
        
        if (rangeInput && rangeValue) {
            rangeInput.addEventListener('input', (e) => {
                rangeValue.textContent = e.target.value;
            });
        }
    }
    
    // Save settings from UI
    saveSettingsFromUI() {
        try {
            // Get values from form
            this.settings.autoSave = document.getElementById('autoSave').checked;
            this.settings.showProgress = document.getElementById('showProgress').checked;
            this.settings.enableNotifications = document.getElementById('enableNotifications').checked;
            this.settings.maxConcurrentRequests = parseInt(document.getElementById('maxConcurrentRequests').value);
            this.settings.retryAttempts = parseInt(document.getElementById('retryAttempts').value);
            this.settings.retryDelay = parseInt(document.getElementById('retryDelay').value);
            this.settings.theme = document.getElementById('theme').value;
            this.settings.language = document.getElementById('language').value;
            
            // Save to localStorage
            if (this.saveSettings()) {
                this.showMessage('تم حفظ الإعدادات بنجاح', 'success');
                
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
                if (modal) {
                    modal.hide();
                }
                
                // Apply theme if changed
                this.applyTheme();
            }
        } catch (error) {
            this.showMessage('خطأ في حفظ الإعدادات: ' + error.message, 'error');
        }
    }
    
    // Update settings UI with current values
    updateSettingsUI() {
        const elements = {
            'autoSave': this.settings.autoSave,
            'showProgress': this.settings.showProgress,
            'enableNotifications': this.settings.enableNotifications,
            'maxConcurrentRequests': this.settings.maxConcurrentRequests,
            'retryAttempts': this.settings.retryAttempts,
            'retryDelay': this.settings.retryDelay,
            'theme': this.settings.theme,
            'language': this.settings.language
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = value;
                } else {
                    element.value = value;
                }
            }
        });
        
        // Update range display
        const rangeValue = document.getElementById('concurrentValue');
        if (rangeValue) {
            rangeValue.textContent = this.settings.maxConcurrentRequests;
        }
    }
    
    // Handle import file selection
    handleImportFile(input) {
        const file = input.files[0];
        if (file) {
            this.importSettings(file).then(() => {
                this.updateSettingsUI();
            }).catch(error => {
                console.error('Import error:', error);
            });
        }
    }
    
    // Apply theme
    applyTheme() {
        const theme = this.settings.theme;
        const body = document.body;
        
        // Remove existing theme classes
        body.classList.remove('theme-light', 'theme-dark');
        
        if (theme === 'dark') {
            body.classList.add('theme-dark');
        } else if (theme === 'light') {
            body.classList.add('theme-light');
        } else {
            // Auto theme - detect system preference
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                body.classList.add('theme-dark');
            } else {
                body.classList.add('theme-light');
            }
        }
    }
    
    // Show settings modal
    showSettingsModal() {
        let modal = document.getElementById('settingsModal');
        if (!modal) {
            modal = this.createSettingsUI();
        }
        
        this.updateSettingsUI();
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
    }
    
    // Show message
    showMessage(message, type) {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
    }
}

// Initialize Settings Manager
const settingsManager = new SettingsManager();

// Register module as loaded
if (typeof window !== 'undefined' && window.loadingManager) {
    window.loadingManager.markLoaded('SettingsManager');
}
