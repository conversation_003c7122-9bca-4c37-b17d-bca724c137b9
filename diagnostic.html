<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص أداة تحليل الملفات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .title {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 15px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d1fae5; color: #065f46; border-left: 4px solid #10b981; }
        .error { background: #fee2e2; color: #991b1b; border-left: 4px solid #ef4444; }
        .warning { background: #fef3c7; color: #92400e; border-left: 4px solid #f59e0b; }
        .info { background: #dbeafe; color: #1e40af; border-left: 4px solid #3b82f6; }
        
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #1d4ed8; }
        
        .file-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .file-item:last-child { border-bottom: none; }
        
        .status-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 10px;
        }
        
        .status-success { background: #10b981; }
        .status-error { background: #ef4444; }
        .status-warning { background: #f59e0b; }
        
        pre {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔍 تشخيص أداة تحليل الملفات</h1>
        
        <div class="test-section">
            <h3>📋 فحص الملفات المطلوبة</h3>
            <button onclick="checkFiles()">فحص الملفات</button>
            <div id="fileCheck"></div>
        </div>
        
        <div class="test-section">
            <h3>🌐 فحص المكتبات الخارجية</h3>
            <button onclick="checkLibraries()">فحص المكتبات</button>
            <div id="libraryCheck"></div>
        </div>
        
        <div class="test-section">
            <h3>⚙️ فحص إعدادات المتصفح</h3>
            <button onclick="checkBrowser()">فحص المتصفح</button>
            <div id="browserCheck"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 حلول سريعة</h3>
            <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                <button onclick="window.open('index-fixed.html', '_blank')">جرب النسخة المحسنة</button>
                <button onclick="window.open('test.html', '_blank')">فتح صفحة الاختبار</button>
                <button onclick="clearCache()">مسح ذاكرة التخزين</button>
                <button onclick="downloadFiles()">تحميل الملفات المفقودة</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 تقرير التشخيص</h3>
            <div id="diagnosticReport"></div>
        </div>
    </div>

    <script>
        const requiredFiles = [
            'config.js',
            'api-manager.js', 
            'file-processor.js',
            'content-splitter.js',
            'preset-instructions.js',
            'export-manager.js',
            'settings-manager.js',
            'script.js',
            'styles.css'
        ];
        
        const requiredLibraries = [
            { name: 'Bootstrap', check: () => typeof bootstrap !== 'undefined', url: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js' },
            { name: 'SheetJS', check: () => typeof XLSX !== 'undefined', url: 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js' },
            { name: 'JSZip', check: () => typeof JSZip !== 'undefined', url: 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js' },
            { name: 'jsPDF', check: () => typeof jsPDF !== 'undefined', url: 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js' }
        ];
        
        async function checkFiles() {
            const container = document.getElementById('fileCheck');
            container.innerHTML = '<p>جاري فحص الملفات...</p>';
            
            let html = '<div class="file-list">';
            let allFilesExist = true;
            
            for (const file of requiredFiles) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    const exists = response.ok;
                    const status = exists ? 'success' : 'error';
                    const statusText = exists ? 'موجود' : 'مفقود';
                    
                    html += `
                        <div class="file-item">
                            <span>
                                <span class="status-icon status-${status}"></span>
                                ${file}
                            </span>
                            <span style="color: ${exists ? '#10b981' : '#ef4444'}">${statusText}</span>
                        </div>
                    `;
                    
                    if (!exists) allFilesExist = false;
                } catch (error) {
                    html += `
                        <div class="file-item">
                            <span>
                                <span class="status-icon status-error"></span>
                                ${file}
                            </span>
                            <span style="color: #ef4444">خطأ في الفحص</span>
                        </div>
                    `;
                    allFilesExist = false;
                }
            }
            
            html += '</div>';
            
            if (allFilesExist) {
                html += '<div class="test-result success">✅ جميع الملفات موجودة</div>';
            } else {
                html += '<div class="test-result error">❌ بعض الملفات مفقودة</div>';
                html += '<p><strong>الحل:</strong> تأكد من وجود جميع الملفات في نفس المجلد مع index.html</p>';
            }
            
            container.innerHTML = html;
        }
        
        function checkLibraries() {
            const container = document.getElementById('libraryCheck');
            let html = '<div class="file-list">';
            let allLibrariesLoaded = true;
            
            for (const lib of requiredLibraries) {
                const loaded = lib.check();
                const status = loaded ? 'success' : 'error';
                const statusText = loaded ? 'محملة' : 'غير محملة';
                
                html += `
                    <div class="file-item">
                        <span>
                            <span class="status-icon status-${status}"></span>
                            ${lib.name}
                        </span>
                        <span style="color: ${loaded ? '#10b981' : '#ef4444'}">${statusText}</span>
                    </div>
                `;
                
                if (!loaded) allLibrariesLoaded = false;
            }
            
            html += '</div>';
            
            if (allLibrariesLoaded) {
                html += '<div class="test-result success">✅ جميع المكتبات محملة</div>';
            } else {
                html += '<div class="test-result error">❌ بعض المكتبات غير محملة</div>';
                html += '<p><strong>الحل:</strong> تحقق من اتصالك بالإنترنت أو جرب النسخة المحسنة</p>';
            }
            
            container.innerHTML = html;
        }
        
        function checkBrowser() {
            const container = document.getElementById('browserCheck');
            let html = '<div class="file-list">';
            
            const checks = [
                { name: 'JavaScript', check: () => true, status: 'success' },
                { name: 'LocalStorage', check: () => typeof Storage !== 'undefined' },
                { name: 'File API', check: () => window.File && window.FileReader && window.FileList && window.Blob },
                { name: 'Fetch API', check: () => typeof fetch !== 'undefined' },
                { name: 'ES6 Support', check: () => typeof Promise !== 'undefined' && typeof Map !== 'undefined' }
            ];
            
            let allSupported = true;
            
            for (const check of checks) {
                const supported = check.check();
                const status = supported ? 'success' : 'error';
                const statusText = supported ? 'مدعوم' : 'غير مدعوم';
                
                html += `
                    <div class="file-item">
                        <span>
                            <span class="status-icon status-${status}"></span>
                            ${check.name}
                        </span>
                        <span style="color: ${supported ? '#10b981' : '#ef4444'}">${statusText}</span>
                    </div>
                `;
                
                if (!supported) allSupported = false;
            }
            
            html += '</div>';
            
            // Browser info
            html += `
                <div class="test-result info">
                    <strong>معلومات المتصفح:</strong><br>
                    ${navigator.userAgent}
                </div>
            `;
            
            if (allSupported) {
                html += '<div class="test-result success">✅ المتصفح يدعم جميع المتطلبات</div>';
            } else {
                html += '<div class="test-result error">❌ المتصفح لا يدعم بعض المتطلبات</div>';
                html += '<p><strong>الحل:</strong> استخدم متصفح حديث مثل Chrome أو Firefox أو Edge</p>';
            }
            
            container.innerHTML = html;
        }
        
        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            // Clear localStorage
            localStorage.clear();
            
            // Clear sessionStorage
            sessionStorage.clear();
            
            alert('تم مسح ذاكرة التخزين المؤقت. يرجى إعادة تحميل الصفحة.');
        }
        
        function downloadFiles() {
            const missingFiles = [
                'تأكد من وجود جميع الملفات التالية في نفس مجلد index.html:',
                '• config.js',
                '• api-manager.js', 
                '• file-processor.js',
                '• content-splitter.js',
                '• preset-instructions.js',
                '• export-manager.js',
                '• settings-manager.js',
                '• script.js',
                '• styles.css'
            ];
            
            alert(missingFiles.join('\n'));
        }
        
        // Auto-run basic checks
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkFiles();
                checkLibraries();
                checkBrowser();
                generateReport();
            }, 500);
        });
        
        function generateReport() {
            const container = document.getElementById('diagnosticReport');
            
            const report = {
                timestamp: new Date().toLocaleString('ar-SA'),
                browser: navigator.userAgent,
                url: window.location.href,
                protocol: window.location.protocol,
                host: window.location.host
            };
            
            container.innerHTML = `
                <pre>${JSON.stringify(report, null, 2)}</pre>
                <p><strong>نصائح:</strong></p>
                <ul>
                    <li>إذا كانت الملفات مفقودة، تأكد من تحميل جميع الملفات</li>
                    <li>إذا كانت المكتبات غير محملة، تحقق من الاتصال بالإنترنت</li>
                    <li>جرب النسخة المحسنة (index-fixed.html) للحصول على تحميل أفضل</li>
                    <li>استخدم متصفح حديث للحصول على أفضل أداء</li>
                </ul>
            `;
        }
    </script>
</body>
</html>
