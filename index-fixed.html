<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة تحليل وتعديل الملفات بالذكاء الصناعي - محرر ملفات احترافي</title>
    <meta name="description" content="أداة احترافية لتحليل وتعديل ملفات HTML, TXT, Markdown, Excel, BAT باستخدام الذكاء الصناعي مع إمكانيات متقدمة للتعامل مع الملفات الكبيرة">
    <meta name="keywords" content="تحليل ملفات, تعديل ملفات, ذكاء صناعي, HTML, TXT, Markdown, Excel, BAT">
    <meta name="author" content="أداة تحليل الملفات">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="أداة تحليل وتعديل الملفات بالذكاء الصناعي">
    <meta property="og:description" content="أداة احترافية لتحليل وتعديل ملفات HTML, TXT, Markdown, Excel, BAT باستخدام الذكاء الصناعي">
    <meta property="og:type" content="website">
    <meta property="og:locale" content="ar_AR">
    
    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "أداة تحليل وتعديل الملفات بالذكاء الصناعي",
        "description": "أداة احترافية لتحليل وتعديل ملفات HTML, TXT, Markdown, Excel, BAT باستخدام الذكاء الصناعي",
        "applicationCategory": "DeveloperApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        },
        "author": {
            "@type": "Organization",
            "name": "أداة تحليل الملفات"
        }
    }
    </script>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="styles.css" rel="stylesheet">
    
    <!-- Loading Styles -->
    <style>
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            color: white;
            font-family: 'Cairo', sans-serif;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }
        
        .loading-progress {
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .loading-progress-bar {
            height: 100%;
            background: white;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .loading-details {
            font-size: 0.9rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-spinner"></div>
        <div class="loading-text">جاري تحميل أداة تحليل الملفات...</div>
        <div class="loading-progress">
            <div id="loadingProgressBar" class="loading-progress-bar"></div>
        </div>
        <div id="loadingDetails" class="loading-details">تحميل المكونات الأساسية...</div>
    </div>

    <!-- Main Content (Hidden initially) -->
    <div id="mainContent" style="display: none;">
        <!-- Header -->
        <header class="header-section">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="main-title">
                            <i class="fas fa-robot text-primary me-3"></i>
                            أداة تحليل وتعديل الملفات بالذكاء الصناعي
                        </h1>
                        <p class="subtitle">محرر ملفات احترافي متقدم يدعم جميع أنواع الملفات</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="header-actions">
                            <button class="btn btn-outline-primary me-2" id="importSettings">
                                <i class="fas fa-upload"></i> استيراد الإعدادات
                            </button>
                            <button class="btn btn-outline-secondary" id="exportSettings">
                                <i class="fas fa-download"></i> تصدير الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div class="container">
                <!-- File Upload Section -->
                <section class="upload-section mb-5">
                    <div class="card shadow-lg">
                        <div class="card-header bg-primary text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-cloud-upload-alt me-2"></i>
                                رفع الملفات
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="upload-area" id="uploadArea">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                    <h4>اسحب الملفات هنا أو انقر للاختيار</h4>
                                    <p>يدعم: HTML, TXT, Markdown, Excel, BAT وملفات أخرى</p>
                                    <input type="file" id="fileInput" multiple accept=".html,.txt,.md,.xlsx,.xls,.bat,.js,.css,.json,.xml,.csv">
                                </div>
                            </div>
                            <div class="uploaded-files mt-3" id="uploadedFiles"></div>
                        </div>
                    </div>
                </section>

                <!-- Instructions Section -->
                <section class="instructions-section mb-5">
                    <div class="card shadow-lg">
                        <div class="card-header bg-success text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-edit me-2"></i>
                                تعليمات التعديل
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="customInstructions" class="form-label">تعليمات مخصصة:</label>
                                    <textarea class="form-control" id="customInstructions" rows="6" 
                                        placeholder="اكتب تعليماتك المخصصة هنا..."></textarea>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">التعليمات المسبقة:</label>
                                    <div class="preset-instructions" id="presetInstructions">
                                        <!-- Will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Processing Section -->
                <section class="processing-section mb-5">
                    <div class="card shadow-lg">
                        <div class="card-header bg-warning text-dark">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-cogs me-2"></i>
                                معالجة الملفات
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="processing-controls mb-4">
                                <button class="btn btn-primary btn-lg me-3" id="startProcessing">
                                    <i class="fas fa-play me-2"></i>
                                    بدء المعالجة
                                </button>
                                <button class="btn btn-warning me-3" id="pauseProcessing" disabled>
                                    <i class="fas fa-pause me-2"></i>
                                    إيقاف مؤقت
                                </button>
                                <button class="btn btn-danger" id="stopProcessing" disabled>
                                    <i class="fas fa-stop me-2"></i>
                                    إيقاف
                                </button>
                            </div>
                            
                            <!-- Progress Bar -->
                            <div class="progress-container mb-4" id="progressContainer" style="display: none;">
                                <div class="progress-info mb-2">
                                    <span class="progress-text" id="progressText">جاري المعالجة...</span>
                                    <span class="progress-percentage" id="progressPercentage">0%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         id="progressBar" role="progressbar" style="width: 0%"></div>
                                </div>
                                <div class="processing-details mt-2">
                                    <small class="text-muted" id="processingDetails">
                                        الملف الحالي: - | المتبقي: - | الوقت المقدر: -
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Results Section -->
                <section class="results-section mb-5" id="resultsSection" style="display: none;">
                    <div class="card shadow-lg">
                        <div class="card-header bg-info text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-file-alt me-2"></i>
                                النتائج
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="results-controls mb-3">
                                <input type="text" class="form-control mb-3" id="searchResults" 
                                       placeholder="البحث في النتائج...">
                            </div>
                            <div class="results-content" id="resultsContent">
                                <!-- Results will be populated here -->
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Export Section -->
                <section class="export-section mb-5" id="exportSection" style="display: none;">
                    <div class="card shadow-lg">
                        <div class="card-header bg-secondary text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-download me-2"></i>
                                تصدير النتائج
                            </h3>
                        </div>
                        <div class="card-body">
                            <div class="export-options">
                                <button class="btn btn-outline-primary me-2 mb-2" id="exportHTML">
                                    <i class="fas fa-code me-2"></i>HTML
                                </button>
                                <button class="btn btn-outline-success me-2 mb-2" id="exportExcel">
                                    <i class="fas fa-file-excel me-2"></i>Excel
                                </button>
                                <button class="btn btn-outline-danger me-2 mb-2" id="exportPDF">
                                    <i class="fas fa-file-pdf me-2"></i>PDF
                                </button>
                                <button class="btn btn-outline-info me-2 mb-2" id="exportTXT">
                                    <i class="fas fa-file-alt me-2"></i>TXT
                                </button>
                                <button class="btn btn-outline-warning" id="exportZIP">
                                    <i class="fas fa-file-archive me-2"></i>ZIP
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer-section">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <p>&copy; 2024 أداة تحليل وتعديل الملفات. جميع الحقوق محفوظة.</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <p>مدعوم بتقنية الذكاء الصناعي المتقدمة</p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <h5 id="loadingText">جاري المعالجة...</h5>
                </div>
            </div>
        </div>
    </div>

    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    
    <!-- Module Loader -->
    <script>
        // Enhanced loading with progress tracking
        let loadingProgress = 0;
        const totalSteps = 8;
        
        function updateLoadingProgress(step, message) {
            loadingProgress = Math.min(step, totalSteps);
            const percentage = Math.round((loadingProgress / totalSteps) * 100);
            
            const progressBar = document.getElementById('loadingProgressBar');
            const details = document.getElementById('loadingDetails');
            
            if (progressBar) progressBar.style.width = percentage + '%';
            if (details) details.textContent = message;
            
            if (loadingProgress >= totalSteps) {
                setTimeout(hideLoadingScreen, 500);
            }
        }
        
        function hideLoadingScreen() {
            const loadingScreen = document.getElementById('loadingScreen');
            const mainContent = document.getElementById('mainContent');
            
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    if (mainContent) {
                        mainContent.style.display = 'block';
                        mainContent.style.opacity = '0';
                        setTimeout(() => {
                            mainContent.style.transition = 'opacity 0.5s ease';
                            mainContent.style.opacity = '1';
                        }, 50);
                    }
                }, 300);
            }
        }
        
        // Load modules sequentially
        async function loadModules() {
            const modules = [
                { file: 'config.js', name: 'الإعدادات الأساسية' },
                { file: 'api-manager.js', name: 'مدير APIs' },
                { file: 'file-processor.js', name: 'معالج الملفات' },
                { file: 'content-splitter.js', name: 'مقسم المحتوى' },
                { file: 'preset-instructions.js', name: 'التعليمات المسبقة' },
                { file: 'export-manager.js', name: 'مدير التصدير' },
                { file: 'settings-manager.js', name: 'مدير الإعدادات' },
                { file: 'script.js', name: 'التطبيق الرئيسي' }
            ];
            
            for (let i = 0; i < modules.length; i++) {
                const module = modules[i];
                updateLoadingProgress(i + 1, `تحميل ${module.name}...`);
                
                try {
                    await loadScript(module.file);
                    await new Promise(resolve => setTimeout(resolve, 200)); // Small delay for visual feedback
                } catch (error) {
                    console.error(`فشل في تحميل ${module.file}:`, error);
                    showLoadingError(`فشل في تحميل ${module.name}`);
                    return;
                }
            }
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = () => reject(new Error(`فشل في تحميل ${src}`));
                document.head.appendChild(script);
            });
        }
        
        function showLoadingError(message) {
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                loadingScreen.innerHTML = `
                    <div style="text-align: center; color: white;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px; color: #fbbf24;"></i>
                        <h2>خطأ في التحميل</h2>
                        <p>${message}</p>
                        <button onclick="location.reload()" style="background: white; color: #667eea; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 10px;">
                            إعادة المحاولة
                        </button>
                        <button onclick="window.open('test.html', '_blank')" style="background: #059669; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 10px;">
                            فتح صفحة الاختبار
                        </button>
                    </div>
                `;
            }
        }
        
        // Start loading when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadModules);
        } else {
            loadModules();
        }
    </script>
</body>
</html>
