# أداة تحليل وتعديل الملفات بالذكاء الصناعي

## نظرة عامة

أداة احترافية متقدمة لتحليل وتعديل الملفات باستخدام تقنيات الذكاء الصناعي. تدعم الأداة أنواع متعددة من الملفات وتوفر إمكانيات متقدمة للمعالجة والتحرير.

## المميزات الرئيسية

### 🚀 دعم أنواع ملفات متعددة
- **HTML** - ملفات صفحات الويب
- **TXT** - الملفات النصية العادية
- **Markdown** - ملفات التوثيق والمحتوى
- **Excel** (.xlsx, .xls) - جداول البيانات
- **BAT** - ملفات الأوامر المجمعة
- **JavaScript** - ملفات البرمجة
- **CSS** - ملفات التنسيق
- **JSON** - ملفات البيانات المنظمة
- **XML** - ملفات البيانات المهيكلة
- **CSV** - ملفات البيانات المفصولة بفواصل

### 🤖 معالجة بالذكاء الصناعي
- استخدام **Gemini API** المتقدم
- تدوير تلقائي بين 15 مفتاح API
- إدارة ذكية لحدود الاستخدام
- معالجة متوازية للملفات الكبيرة

### 📋 تعليمات مسبقة جاهزة
- **تحسين السيو** - تحسين محركات البحث
- **تحسين التنسيق** - تنسيق الكود والمحتوى
- **إزالة أكواد HTML** - تنظيف المحتوى
- **حذف الروابط** - إزالة الروابط غير المرغوبة
- **حذف التكرار** - إزالة المحتوى المكرر
- **ضغط الكود** - تقليل حجم الملفات
- **إنشاء متغيرات البروفايل** - خاص بملفات BAT
- **تحسين القابلية للقراءة** - تحسين تجربة القراءة

### 📤 تصدير متعدد التنسيقات
- **HTML** - صفحة ويب تفاعلية مع محرك بحث
- **Excel** - جداول بيانات منظمة
- **PDF** - مستندات قابلة للطباعة
- **TXT** - ملفات نصية بسيطة
- **ZIP** - أرشيف شامل لجميع التنسيقات

### ⚙️ إعدادات متقدمة
- حفظ واستيراد الإعدادات
- تخصيص عدد الطلبات المتزامنة
- إدارة محاولات الإعادة
- دعم المظاهر (فاتح/داكن/تلقائي)
- تفعيل/إلغاء الإشعارات

## كيفية الاستخدام

### 1. رفع الملفات
- اسحب الملفات إلى منطقة الرفع أو انقر للاختيار
- يمكن رفع ملفات متعددة في نفس الوقت
- الأداة تدعم الملفات حتى أحجام كبيرة

### 2. إدخال التعليمات
- **تعليمات مخصصة**: اكتب تعليماتك الخاصة
- **تعليمات مسبقة**: اختر من القائمة الجاهزة
- يمكن الجمع بين النوعين

### 3. بدء المعالجة
- انقر "بدء المعالجة"
- راقب التقدم عبر شريط التقدم
- يمكن الإيقاف المؤقت أو الإيقاف الكامل

### 4. مراجعة النتائج
- استعرض النتائج في القسم المخصص
- استخدم محرك البحث للعثور على محتوى محدد
- راجع التعديلات المطبقة

### 5. تصدير النتائج
- اختر التنسيق المطلوب
- احصل على ملفاتك المعدلة فوراً

## مثال عملي: ملفات BAT

### المشكلة
لديك ملف BAT يحتوي على:
```batch
@echo off
start "" "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --profile-directory="Profile 6"
```

### الحل
1. ارفع الملف
2. اختر "إنشاء متغيرات البروفايل" من التعليمات المسبقة
3. أو اكتب تعليمة مخصصة: "أنشئ 5 نسخ من هذا الملف مع تغيير رقم البروفايل من 6 إلى 7, 8, 9, 10, 11"

### النتيجة
ستحصل على ملفات متعددة:
```batch
@echo off
start "" "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --profile-directory="Profile 7"
```
```batch
@echo off
start "" "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --profile-directory="Profile 8"
```
وهكذا...

## المتطلبات التقنية

### المتصفحات المدعومة
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### الاتصال بالإنترنت
- مطلوب للوصول إلى Gemini API
- مطلوب لتحميل المكتبات الخارجية

## الأمان والخصوصية

- **معالجة محلية**: الملفات تُعالج في متصفحك
- **عدم التخزين**: لا يتم حفظ ملفاتك على الخوادم
- **تشفير الاتصال**: جميع الاتصالات مشفرة
- **مفاتيح API آمنة**: مدارة بطريقة آمنة

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### "تم الوصول لحد استخدام API"
- الأداة تتبدل تلقائياً بين المفاتيح
- انتظر قليلاً وأعد المحاولة

#### "خطأ في قراءة الملف"
- تأكد من أن الملف غير تالف
- تأكد من دعم نوع الملف

#### "الملف كبير جداً"
- الأداة تقسم الملفات الكبيرة تلقائياً
- قد تستغرق المعالجة وقتاً أطول

#### "لا تظهر النتائج"
- تأكد من اكتمال المعالجة
- تحقق من وجود أخطاء في وحدة التحكم

## التطوير والمساهمة

### هيكل المشروع
```
├── index.html              # الصفحة الرئيسية
├── styles.css              # ملف التنسيق
├── config.js               # إعدادات التطبيق
├── api-manager.js          # إدارة APIs
├── file-processor.js       # معالج الملفات
├── content-splitter.js     # مقسم المحتوى
├── preset-instructions.js  # التعليمات المسبقة
├── export-manager.js       # مدير التصدير
├── settings-manager.js     # مدير الإعدادات
└── script.js              # الملف الرئيسي
```

### إضافة نوع ملف جديد
1. أضف النوع في `CONFIG.SUPPORTED_FILE_TYPES`
2. أنشئ معالج في `file-processor.js`
3. أضف دعم التقسيم في `content-splitter.js`

### إضافة تعليمة مسبقة جديدة
1. أضف التعليمة في `preset-instructions.js`
2. حدد الملفات المطبقة عليها
3. اكتب الوصف والتعليمات

## الدعم والمساعدة

### موارد مفيدة
- [وثائق Gemini API](https://ai.google.dev/docs)
- [مرجع Bootstrap](https://getbootstrap.com/docs/5.3/)
- [دليل JavaScript](https://developer.mozilla.org/en-US/docs/Web/JavaScript)

### الإبلاغ عن المشاكل
إذا واجهت أي مشاكل:
1. تحقق من وحدة التحكم في المتصفح
2. تأكد من الاتصال بالإنترنت
3. جرب إعادة تحميل الصفحة
4. تحقق من إعدادات المتصفح

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**تم تطوير هذه الأداة بعناية لتوفير تجربة مستخدم متميزة في تحليل وتعديل الملفات باستخدام أحدث تقنيات الذكاء الصناعي.**
