/* Global Styles */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 12px;
    --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
    line-height: 1.6;
}

/* Header Styles */
.header-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem 0;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

.main-title {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    color: var(--dark-color);
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.subtitle {
    color: var(--secondary-color);
    font-size: 1.1rem;
    font-weight: 400;
}

.header-actions .btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
}

.header-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Main Content */
.main-content {
    padding: 2rem 0;
}

/* Card Styles */
.card {
    border: none;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-header {
    border: none;
    padding: 1.5rem;
    font-weight: 600;
}

.card-title {
    font-family: 'Cairo', sans-serif;
    font-size: 1.3rem;
}

.card-body {
    padding: 2rem;
}

/* Upload Area */
.upload-area {
    border: 3px dashed var(--primary-color);
    border-radius: var(--border-radius);
    padding: 3rem;
    text-align: center;
    background: linear-gradient(45deg, rgba(37, 99, 235, 0.05), rgba(37, 99, 235, 0.1));
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(37, 99, 235, 0.1), transparent);
    transform: rotate(45deg);
    transition: var(--transition);
    opacity: 0;
}

.upload-area:hover::before {
    opacity: 1;
    animation: shimmer 2s infinite;
}

.upload-area:hover {
    border-color: var(--success-color);
    background: linear-gradient(45deg, rgba(5, 150, 105, 0.05), rgba(5, 150, 105, 0.1));
    transform: scale(1.02);
}

.upload-area.dragover {
    border-color: var(--success-color);
    background: linear-gradient(45deg, rgba(5, 150, 105, 0.1), rgba(5, 150, 105, 0.2));
    transform: scale(1.05);
}

.upload-icon {
    font-size: 4rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    transition: var(--transition);
}

.upload-area:hover .upload-icon {
    color: var(--success-color);
    transform: scale(1.1);
}

.upload-content h4 {
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.upload-content p {
    color: var(--secondary-color);
    margin-bottom: 0;
}

#fileInput {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* Uploaded Files */
.uploaded-files {
    max-height: 300px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--light-color);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.file-item:hover {
    background: #e2e8f0;
    transform: translateX(-5px);
}

.file-info {
    display: flex;
    align-items: center;
}

.file-icon {
    font-size: 1.5rem;
    margin-left: 1rem;
    color: var(--primary-color);
}

.file-details h6 {
    margin: 0;
    color: var(--dark-color);
    font-weight: 600;
}

.file-details small {
    color: var(--secondary-color);
}

.file-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: var(--transition);
    font-family: 'Cairo', sans-serif;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    transform: translateY(-2px);
}

/* Preset Instructions */
.preset-instructions {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 1rem;
}

.preset-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.preset-item:last-child {
    border-bottom: none;
}

.preset-item input[type="checkbox"] {
    margin-left: 0.5rem;
    transform: scale(1.2);
}

.preset-item label {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
    color: var(--dark-color);
    transition: var(--transition);
}

.preset-item:hover label {
    color: var(--primary-color);
}

/* Progress Bar */
.progress-container {
    background: var(--light-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid #e2e8f0;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.progress-text {
    font-weight: 600;
    color: var(--dark-color);
}

.progress-percentage {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.progress {
    height: 12px;
    border-radius: 6px;
    background: #e2e8f0;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    transition: width 0.3s ease;
}

.processing-details {
    text-align: center;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn:active {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Results Section */
.results-content {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    background: var(--light-color);
}

.result-item {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: var(--transition);
}

.result-item:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.result-title {
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.result-content {
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

/* Export Options */
.export-options .btn {
    margin: 0.25rem;
    min-width: 120px;
}

/* Footer */
.footer-section {
    background: rgba(30, 41, 59, 0.95);
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
    backdrop-filter: blur(10px);
}

/* Animations */
@keyframes shimmer {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(100%) rotate(45deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-title {
        font-size: 2rem;
    }
    
    .header-actions {
        margin-top: 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--info-color);
}

/* Dark Theme Support */
.theme-dark {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    color: #e2e8f0;
}

.theme-dark .card {
    background: rgba(30, 41, 59, 0.95);
    color: #e2e8f0;
}

.theme-dark .form-control,
.theme-dark .form-select {
    background: rgba(30, 41, 59, 0.8);
    border-color: #475569;
    color: #e2e8f0;
}

.theme-dark .form-control:focus,
.theme-dark .form-select:focus {
    background: rgba(30, 41, 59, 0.9);
    border-color: var(--primary-color);
    color: #e2e8f0;
}

.theme-dark .upload-area {
    background: linear-gradient(45deg, rgba(37, 99, 235, 0.1), rgba(37, 99, 235, 0.2));
    border-color: var(--primary-color);
}

.theme-dark .file-item {
    background: rgba(51, 65, 85, 0.8);
}

.theme-dark .file-item:hover {
    background: rgba(51, 65, 85, 1);
}

/* Loading Animations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

/* Interactive Elements */
.interactive-hover {
    transition: all 0.3s ease;
    cursor: pointer;
}

.interactive-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-left: 8px;
}

.status-success { background: var(--success-color); }
.status-warning { background: var(--warning-color); }
.status-error { background: var(--danger-color); }
.status-processing {
    background: var(--primary-color);
    animation: pulse 2s infinite;
}

/* Enhanced Progress Bar */
.progress-enhanced {
    position: relative;
    overflow: visible;
}

.progress-enhanced::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent, rgba(37, 99, 235, 0.3), transparent);
    border-radius: 8px;
    animation: progressGlow 2s ease-in-out infinite;
}

@keyframes progressGlow {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Notification Styles */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.notification {
    margin-bottom: 10px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    animation: slideInRight 0.3s ease-out;
}

.notification.removing {
    animation: slideOutRight 0.3s ease-in forwards;
}

@keyframes slideOutRight {
    to { transform: translateX(100%); opacity: 0; }
}
