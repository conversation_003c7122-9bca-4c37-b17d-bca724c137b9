<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة تحليل وتعديل الملفات بالذكاء الصناعي</title>
    <meta name="description" content="أداة احترافية لتحليل وتعديل ملفات HTML, TXT, Markdown, Excel, BAT باستخدام الذكاء الصناعي">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
            --border-radius: 12px;
            --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            line-height: 1.6;
        }

        .header-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: var(--box-shadow);
        }

        .main-title {
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
            color: var(--dark-color);
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: var(--secondary-color);
            font-size: 1.1rem;
            font-weight: 400;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            overflow: hidden;
            transition: var(--transition);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            border: none;
            padding: 1.5rem;
            font-weight: 600;
        }

        .card-body {
            padding: 2rem;
        }

        .upload-area {
            border: 3px dashed var(--primary-color);
            border-radius: var(--border-radius);
            padding: 3rem;
            text-align: center;
            background: linear-gradient(45deg, rgba(37, 99, 235, 0.05), rgba(37, 99, 235, 0.1));
            transition: var(--transition);
            cursor: pointer;
            position: relative;
        }

        .upload-area:hover {
            border-color: var(--success-color);
            background: linear-gradient(45deg, rgba(5, 150, 105, 0.05), rgba(5, 150, 105, 0.1));
            transform: scale(1.02);
        }

        .upload-area.dragover {
            border-color: var(--success-color);
            background: linear-gradient(45deg, rgba(5, 150, 105, 0.1), rgba(5, 150, 105, 0.2));
            transform: scale(1.05);
        }

        .upload-icon {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
            transition: var(--transition);
        }

        .upload-area:hover .upload-icon {
            color: var(--success-color);
            transform: scale(1.1);
        }

        #fileInput {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .form-control, .form-select {
            border-radius: var(--border-radius);
            border: 2px solid #e2e8f0;
            padding: 0.75rem 1rem;
            transition: var(--transition);
            font-family: 'Cairo', sans-serif;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
            transform: translateY(-2px);
        }

        .btn {
            border-radius: var(--border-radius);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            transition: var(--transition);
            border: none;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .btn-lg {
            padding: 1rem 2rem;
            font-size: 1.1rem;
        }

        .progress {
            height: 12px;
            border-radius: 6px;
            background: #e2e8f0;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            transition: width 0.3s ease;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: var(--light-color);
            border-radius: var(--border-radius);
            margin-bottom: 0.5rem;
            transition: var(--transition);
        }

        .file-item:hover {
            background: #e2e8f0;
            transform: translateX(-5px);
        }

        .file-info {
            display: flex;
            align-items: center;
        }

        .file-icon {
            font-size: 1.5rem;
            margin-left: 1rem;
            color: var(--primary-color);
        }

        .preset-instructions {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: var(--border-radius);
            padding: 1rem;
        }

        .preset-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f1f5f9;
        }

        .preset-item:last-child {
            border-bottom: none;
        }

        .preset-item input[type="checkbox"] {
            margin-left: 0.5rem;
            transform: scale(1.2);
        }

        .preset-item label {
            margin: 0;
            cursor: pointer;
            font-weight: 500;
            color: var(--dark-color);
            transition: var(--transition);
        }

        .preset-item:hover label {
            color: var(--primary-color);
        }

        .results-content {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            background: var(--light-color);
        }

        .result-item {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: var(--transition);
        }

        .result-item:hover {
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .result-title {
            font-weight: 600;
            color: var(--dark-color);
            margin: 0;
        }

        .result-content {
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            background: #f8fafc;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
            max-height: 300px;
            overflow-y: auto;
        }

        .footer-section {
            background: rgba(30, 41, 59, 0.95);
            color: white;
            padding: 2rem 0;
            margin-top: 3rem;
            backdrop-filter: blur(10px);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @media (max-width: 768px) {
            .main-title {
                font-size: 2rem;
            }
            
            .card-body {
                padding: 1.5rem;
            }
            
            .upload-area {
                padding: 2rem 1rem;
            }
        }

        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--info-color);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="main-title">
                        <i class="fas fa-robot text-primary me-3"></i>
                        أداة تحليل وتعديل الملفات بالذكاء الصناعي
                    </h1>
                    <p class="subtitle">محرر ملفات احترافي متقدم يدعم جميع أنواع الملفات</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="header-actions">
                        <button class="btn btn-outline-primary me-2" onclick="importSettings()">
                            <i class="fas fa-upload"></i> استيراد الإعدادات
                        </button>
                        <button class="btn btn-outline-secondary" onclick="exportSettings()">
                            <i class="fas fa-download"></i> تصدير الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- File Upload Section -->
            <section class="upload-section">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-cloud-upload-alt me-2"></i>
                            رفع الملفات
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <h4>اسحب الملفات هنا أو انقر للاختيار</h4>
                                <p>يدعم: HTML, TXT, Markdown, Excel, BAT وملفات أخرى</p>
                                <input type="file" id="fileInput" multiple accept=".html,.txt,.md,.xlsx,.xls,.bat,.js,.css,.json,.xml,.csv">
                            </div>
                        </div>
                        <div class="uploaded-files mt-3" id="uploadedFiles"></div>
                    </div>
                </div>
            </section>

            <!-- Instructions Section -->
            <section class="instructions-section">
                <div class="card shadow-lg">
                    <div class="card-header bg-success text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i>
                            تعليمات التعديل
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="customInstructions" class="form-label">تعليمات مخصصة:</label>
                                <textarea class="form-control" id="customInstructions" rows="6" 
                                    placeholder="اكتب تعليماتك المخصصة هنا..."></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">التعليمات المسبقة:</label>
                                <div class="preset-instructions" id="presetInstructions">
                                    <!-- Will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Processing Section -->
            <section class="processing-section">
                <div class="card shadow-lg">
                    <div class="card-header bg-warning text-dark">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            معالجة الملفات
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="processing-controls mb-4">
                            <button class="btn btn-primary btn-lg me-3" id="startProcessing">
                                <i class="fas fa-play me-2"></i>
                                بدء المعالجة
                            </button>
                            <button class="btn btn-warning me-3" id="pauseProcessing" disabled>
                                <i class="fas fa-pause me-2"></i>
                                إيقاف مؤقت
                            </button>
                            <button class="btn btn-danger" id="stopProcessing" disabled>
                                <i class="fas fa-stop me-2"></i>
                                إيقاف
                            </button>
                        </div>
                        
                        <!-- Progress Bar -->
                        <div class="progress-container mb-4" id="progressContainer" style="display: none;">
                            <div class="progress-info mb-2">
                                <span class="progress-text" id="progressText">جاري المعالجة...</span>
                                <span class="progress-percentage" id="progressPercentage">0%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="processing-details mt-2">
                                <small class="text-muted" id="processingDetails">
                                    الملف الحالي: - | المتبقي: - | الوقت المقدر: -
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="card shadow-lg">
                    <div class="card-header bg-info text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            النتائج
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="results-controls mb-3">
                            <input type="text" class="form-control mb-3" id="searchResults" 
                                   placeholder="البحث في النتائج...">
                        </div>
                        <div class="results-content" id="resultsContent">
                            <!-- Results will be populated here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Export Section -->
            <section class="export-section" id="exportSection" style="display: none;">
                <div class="card shadow-lg">
                    <div class="card-header bg-secondary text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-download me-2"></i>
                            تصدير النتائج
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="export-options">
                            <button class="btn btn-outline-primary me-2 mb-2" onclick="exportToHTML()">
                                <i class="fas fa-code me-2"></i>HTML
                            </button>
                            <button class="btn btn-outline-success me-2 mb-2" onclick="exportToTXT()">
                                <i class="fas fa-file-alt me-2"></i>TXT
                            </button>
                            <button class="btn btn-outline-info me-2 mb-2" onclick="copyToClipboard()">
                                <i class="fas fa-copy me-2"></i>نسخ
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer-section">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>&copy; 2024 أداة تحليل وتعديل الملفات. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>مدعوم بتقنية الذكاء الصناعي المتقدمة</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Configuration
        const CONFIG = {
            GEMINI_API_KEYS: [
                'AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ',
                'AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg',
                'AIzaSyCH5jU2QrczZOLPqNsceh_um6qs_N3Skgw',
                'AIzaSyCTYMFClrP1A1by2rTXTRYjl3mi-FfVvak',
                'AIzaSyDzSRdhUClIXUWpi9QRKi0hfB29_bzaQAc'
            ],
            GEMINI_API_URL: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
            MAX_TOKENS_PER_REQUEST: 30000,
            CHUNK_SIZE: 25000,
            SUPPORTED_FILE_TYPES: {
                'text/plain': { extension: '.txt', icon: 'fas fa-file-alt', processor: 'text' },
                'text/html': { extension: '.html', icon: 'fab fa-html5', processor: 'html' },
                'text/markdown': { extension: '.md', icon: 'fab fa-markdown', processor: 'markdown' },
                'application/x-bat': { extension: '.bat', icon: 'fas fa-terminal', processor: 'batch' },
                'text/javascript': { extension: '.js', icon: 'fab fa-js-square', processor: 'javascript' },
                'text/css': { extension: '.css', icon: 'fab fa-css3-alt', processor: 'css' },
                'application/json': { extension: '.json', icon: 'fas fa-code', processor: 'json' }
            }
        };

        // Global variables
        let uploadedFiles = [];
        let processedResults = [];
        let isProcessing = false;
        let isPaused = false;
        let currentApiKeyIndex = 0;
        let selectedPresets = new Set();

        // Preset instructions
        const presetInstructions = {
            'create-profile-variants': {
                name: 'إنشاء متغيرات البروفايل',
                description: 'إنشاء عدة نسخ من ملف BAT بمتغيرات مختلفة للبروفايل',
                applicable: ['batch'],
                isDefault: true
            },
            'improve-seo': {
                name: 'تحسين السيو',
                description: 'تحسين محركات البحث للملفات HTML',
                applicable: ['html']
            },
            'remove-html-codes': {
                name: 'إزالة أكواد HTML',
                description: 'إزالة أكواد HTML من المحتوى',
                applicable: ['text', 'markdown']
            },
            'remove-links': {
                name: 'حذف الروابط',
                description: 'إزالة جميع الروابط من المحتوى',
                applicable: ['text', 'html', 'markdown']
            },
            'remove-duplicates': {
                name: 'حذف التكرار',
                description: 'إزالة الأسطر والمحتوى المكرر',
                applicable: ['text']
            },
            'remove-empty-lines': {
                name: 'حذف الأسطر الفارغة',
                description: 'إزالة الأسطر الفارغة الزائدة',
                applicable: ['text', 'html', 'css', 'javascript']
            },
            'compress-code': {
                name: 'ضغط الكود',
                description: 'ضغط الكود وتقليل حجمه',
                applicable: ['css', 'javascript', 'html']
            }
        };

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            setupEventListeners();
            renderPresetInstructions();
            showMessage('مرحباً بك في أداة تحليل وتعديل الملفات بالذكاء الصناعي', 'info');
        }

        function setupEventListeners() {
            // File upload
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');
            
            fileInput.addEventListener('change', handleFileSelect);
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleFileDrop);
            uploadArea.addEventListener('click', () => fileInput.click());
            
            // Processing controls
            document.getElementById('startProcessing').addEventListener('click', startProcessing);
            document.getElementById('pauseProcessing').addEventListener('click', pauseProcessing);
            document.getElementById('stopProcessing').addEventListener('click', stopProcessing);
            
            // Search
            document.getElementById('searchResults').addEventListener('input', (e) => searchResults(e.target.value));
        }

        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            addFiles(files);
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
        }

        function handleFileDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            const files = Array.from(event.dataTransfer.files);
            addFiles(files);
        }

        function addFiles(files) {
            const validFiles = files.filter(file => {
                const isSupported = isFileTypeSupported(file.type) || isFileExtensionSupported(file.name);
                if (!isSupported) {
                    showMessage(`نوع الملف غير مدعوم: ${file.name}`, 'warning');
                    return false;
                }
                return true;
            });
            
            uploadedFiles.push(...validFiles);
            updateFilesList();
            showMessage(`تم إضافة ${validFiles.length} ملف بنجاح`, 'success');
        }

        function isFileTypeSupported(mimeType) {
            return CONFIG.SUPPORTED_FILE_TYPES.hasOwnProperty(mimeType);
        }

        function isFileExtensionSupported(filename) {
            const extension = '.' + filename.split('.').pop().toLowerCase();
            return Object.values(CONFIG.SUPPORTED_FILE_TYPES).some(type => type.extension === extension);
        }

        function updateFilesList() {
            const container = document.getElementById('uploadedFiles');
            if (!container) return;
            
            if (uploadedFiles.length === 0) {
                container.innerHTML = '';
                return;
            }
            
            let html = '<h6 class="mb-3">الملفات المرفوعة:</h6>';
            
            uploadedFiles.forEach((file, index) => {
                const fileType = getFileTypeConfig(file.type);
                const icon = fileType ? fileType.icon : getFileIcon('.' + file.name.split('.').pop());
                
                html += `
                    <div class="file-item">
                        <div class="file-info">
                            <i class="${icon} file-icon"></i>
                            <div class="file-details">
                                <h6>${file.name}</h6>
                                <small>${formatFileSize(file.size)} - ${new Date(file.lastModified).toLocaleString('ar-SA')}</small>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            updateFilesList();
            showMessage('تم حذف الملف', 'info');
        }

        function getFileTypeConfig(mimeType) {
            return CONFIG.SUPPORTED_FILE_TYPES[mimeType] || null;
        }

        function getFileIcon(extension) {
            const fileType = Object.values(CONFIG.SUPPORTED_FILE_TYPES)
                .find(type => type.extension === extension);
            return fileType ? fileType.icon : 'fas fa-file';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 بايت';
            const k = 1024;
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function renderPresetInstructions() {
            const container = document.getElementById('presetInstructions');
            if (!container) return;
            
            let html = '';
            
            Object.entries(presetInstructions).forEach(([key, preset]) => {
                const isSelected = selectedPresets.has(key);
                const isDefault = preset.isDefault ? ' (افتراضي)' : '';
                
                html += `
                    <div class="preset-item">
                        <input type="checkbox" 
                               id="preset-${key}" 
                               class="form-check-input me-2" 
                               ${isSelected ? 'checked' : ''}
                               onchange="togglePreset('${key}')">
                        <label for="preset-${key}" class="form-check-label flex-grow-1">
                            <strong>${preset.name}${isDefault}</strong>
                            <br>
                            <small class="text-muted">${preset.description}</small>
                        </label>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function togglePreset(presetKey) {
            if (selectedPresets.has(presetKey)) {
                selectedPresets.delete(presetKey);
            } else {
                selectedPresets.add(presetKey);
            }
        }

        async function startProcessing() {
            if (uploadedFiles.length === 0) {
                showMessage('يرجى اختيار ملف واحد على الأقل', 'warning');
                return;
            }
            
            const customInstructions = document.getElementById('customInstructions').value.trim();
            const selectedPresetsArray = Array.from(selectedPresets);
            
            if (!customInstructions && selectedPresetsArray.length === 0) {
                showMessage('يرجى إدخال تعليمات التعديل', 'warning');
                return;
            }
            
            isProcessing = true;
            isPaused = false;
            processedResults = [];
            
            updateProcessingUI();
            showProgressContainer();
            
            try {
                await processAllFiles(customInstructions, selectedPresetsArray);
                showMessage('تم الانتهاء من معالجة جميع الملفات', 'success');
                showResults();
            } catch (error) {
                showMessage('حدث خطأ أثناء المعالجة: ' + error.message, 'error');
            } finally {
                isProcessing = false;
                updateProcessingUI();
            }
        }

        async function processAllFiles(customInstructions, selectedPresetsArray) {
            const totalFiles = uploadedFiles.length;
            
            for (let i = 0; i < totalFiles; i++) {
                if (!isProcessing) break;
                
                while (isPaused) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                updateProgress(i, totalFiles);
                
                try {
                    const file = uploadedFiles[i];
                    const result = await processFile(file, customInstructions, selectedPresetsArray);
                    processedResults.push(result);
                    showMessage(`تم معالجة الملف: ${file.name}`, 'success');
                } catch (error) {
                    console.error(`خطأ في معالجة الملف ${uploadedFiles[i].name}:`, error);
                    showMessage(`خطأ في معالجة الملف ${uploadedFiles[i].name}: ${error.message}`, 'error');
                }
            }
        }

        async function processFile(file, customInstructions, selectedPresetsArray) {
            const content = await readFileContent(file);
            let processedContent = content;
            
            // Apply preset instructions
            if (selectedPresetsArray.length > 0) {
                processedContent = await applyPresetInstructions(processedContent, selectedPresetsArray);
            }
            
            // Apply custom instructions
            if (customInstructions) {
                processedContent = await processWithAI(processedContent, customInstructions, file.name);
            }
            
            return {
                originalName: file.name,
                processedContent: processedContent,
                fileType: getFileTypeConfig(file.type),
                size: file.size,
                lastModified: file.lastModified
            };
        }

        function readFileContent(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result);
                reader.onerror = (e) => reject(new Error('خطأ في قراءة الملف'));
                reader.readAsText(file, 'UTF-8');
            });
        }

        async function applyPresetInstructions(content, selectedPresetsArray) {
            let processedContent = content;
            
            for (const presetKey of selectedPresetsArray) {
                switch (presetKey) {
                    case 'create-profile-variants':
                        processedContent = createProfileVariants(processedContent);
                        break;
                    case 'remove-empty-lines':
                        processedContent = processedContent.replace(/^\s*[\r\n]/gm, '');
                        break;
                    case 'remove-duplicates':
                        const lines = processedContent.split('\n');
                        const uniqueLines = [...new Set(lines)];
                        processedContent = uniqueLines.join('\n');
                        break;
                    case 'remove-links':
                        processedContent = processedContent.replace(/https?:\/\/[^\s]+/g, '');
                        break;
                    case 'remove-html-codes':
                        processedContent = processedContent
                            .replace(/<[^>]*>/g, '')
                            .replace(/&[a-zA-Z0-9#]+;/g, '');
                        break;
                }
            }
            
            return processedContent;
        }

        function createProfileVariants(content) {
            if (!content.includes('Profile 6')) {
                return content;
            }
            
            let variants = [];
            for (let i = 7; i <= 11; i++) {
                variants.push(content.replace(/Profile 6/g, `Profile ${i}`));
            }
            
            return variants.join('\n\n--- ملف جديد ---\n\n');
        }

        async function processWithAI(content, instructions, fileName) {
            const prompt = `أنت محرر ملفات احترافي متخصص. مهمتك هي تعديل المحتوى التالي وفقاً للتعليمات المحددة مع الحفاظ على التنسيق والبنية الأصلية.

معلومات الملف:
- اسم الملف: ${fileName}

التعليمات:
${instructions}

المحتوى الأصلي:
${content}

متطلبات مهمة:
1. احتفظ بالتنسيق والبنية الأصلية للملف
2. لا تغير إلا ما هو مطلوب في التعليمات فقط
3. تأكد من صحة ودقة التعديلات 100%
4. لا تضف أي تعليقات أو توضيحات من الذكاء الصناعي
5. أظهر المحتوى المعدل فقط بدون مقدمات

المحتوى المعدل:`;

            return await makeAPIRequest(prompt);
        }

        async function makeAPIRequest(prompt) {
            const apiKey = CONFIG.GEMINI_API_KEYS[currentApiKeyIndex];
            
            const requestData = {
                contents: [{
                    parts: [{ text: prompt }]
                }],
                generationConfig: {
                    temperature: 0.7,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 8192,
                }
            };
            
            try {
                const response = await fetch(`${CONFIG.GEMINI_API_URL}?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });
                
                if (!response.ok) {
                    // Try next API key
                    currentApiKeyIndex = (currentApiKeyIndex + 1) % CONFIG.GEMINI_API_KEYS.length;
                    throw new Error(`API Error: ${response.status}`);
                }
                
                const result = await response.json();
                
                if (result.candidates && result.candidates.length > 0) {
                    return result.candidates[0].content.parts[0].text;
                } else {
                    throw new Error('لم يتم الحصول على استجابة صالحة من API');
                }
                
            } catch (error) {
                console.error('خطأ في API:', error);
                
                // Try next API key
                currentApiKeyIndex = (currentApiKeyIndex + 1) % CONFIG.GEMINI_API_KEYS.length;
                
                if (error.message.includes('429') || error.message.includes('quota')) {
                    throw new Error('تم الوصول لحد الاستخدام، جاري المحاولة مع مفتاح آخر...');
                }
                
                throw error;
            }
        }

        function pauseProcessing() {
            isPaused = !isPaused;
            updateProcessingUI();
            const message = isPaused ? 'تم إيقاف المعالجة مؤقتاً' : 'تم استئناف المعالجة';
            showMessage(message, 'info');
        }

        function stopProcessing() {
            isProcessing = false;
            isPaused = false;
            updateProcessingUI();
            hideProgressContainer();
            showMessage('تم إيقاف المعالجة', 'warning');
        }

        function updateProcessingUI() {
            const startBtn = document.getElementById('startProcessing');
            const pauseBtn = document.getElementById('pauseProcessing');
            const stopBtn = document.getElementById('stopProcessing');
            
            if (startBtn) startBtn.disabled = isProcessing;
            if (pauseBtn) {
                pauseBtn.disabled = !isProcessing;
                pauseBtn.innerHTML = isPaused ? 
                    '<i class="fas fa-play me-2"></i>استئناف' : 
                    '<i class="fas fa-pause me-2"></i>إيقاف مؤقت';
            }
            if (stopBtn) stopBtn.disabled = !isProcessing;
        }

        function showProgressContainer() {
            const container = document.getElementById('progressContainer');
            if (container) {
                container.style.display = 'block';
            }
        }

        function hideProgressContainer() {
            const container = document.getElementById('progressContainer');
            if (container) {
                container.style.display = 'none';
            }
        }

        function updateProgress(current, total) {
            const progress = ((current + 1) / total) * 100;
            
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            const progressPercentage = document.getElementById('progressPercentage');
            const processingDetails = document.getElementById('processingDetails');
            
            if (progressBar) progressBar.style.width = progress + '%';
            if (progressPercentage) progressPercentage.textContent = Math.round(progress) + '%';
            
            if (progressText) {
                const currentFile = uploadedFiles[current];
                progressText.textContent = `معالجة: ${currentFile?.name || 'غير محدد'}`;
            }
            
            if (processingDetails) {
                const remaining = total - current - 1;
                processingDetails.innerHTML = `
                    الملف الحالي: ${current + 1}/${total} | 
                    المتبقي: ${remaining}
                `;
            }
        }

        function showResults() {
            const resultsSection = document.getElementById('resultsSection');
            const exportSection = document.getElementById('exportSection');
            const resultsContent = document.getElementById('resultsContent');
            
            if (resultsSection) resultsSection.style.display = 'block';
            if (exportSection) exportSection.style.display = 'block';
            
            if (resultsContent && processedResults.length > 0) {
                let html = '';
                
                processedResults.forEach((result, index) => {
                    html += `
                        <div class="result-item" data-index="${index}">
                            <div class="result-header">
                                <h6 class="result-title">${result.originalName}</h6>
                                <small class="text-muted">${result.fileType?.extension || 'غير محدد'}</small>
                            </div>
                            <div class="result-content">${escapeHtml(result.processedContent || '')}</div>
                        </div>
                    `;
                });
                
                resultsContent.innerHTML = html;
            }
        }

        function searchResults(query) {
            const resultItems = document.querySelectorAll('.result-item');
            const searchTerm = query.toLowerCase();
            
            resultItems.forEach(item => {
                const content = item.textContent.toLowerCase();
                if (content.includes(searchTerm) || !searchTerm) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function exportToHTML() {
            if (processedResults.length === 0) {
                showMessage('لا توجد نتائج للتصدير', 'warning');
                return;
            }
            
            let htmlContent = `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج تحليل وتعديل الملفات</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .result { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .result-title { color: #2563eb; border-bottom: 2px solid #2563eb; padding-bottom: 10px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 4px; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>نتائج تحليل وتعديل الملفات</h1>
    <p>تم إنشاء هذا التقرير في: ${new Date().toLocaleString('ar-SA')}</p>
            `;
            
            processedResults.forEach((result, index) => {
                htmlContent += `
    <div class="result">
        <h2 class="result-title">ملف ${index + 1}: ${result.originalName}</h2>
        <p><strong>نوع الملف:</strong> ${result.fileType?.extension || 'غير محدد'}</p>
        <p><strong>الحجم:</strong> ${formatFileSize(result.size || 0)}</p>
        <h3>المحتوى المعدل:</h3>
        <pre>${escapeHtml(result.processedContent || '')}</pre>
    </div>
                `;
            });
            
            htmlContent += `
</body>
</html>
            `;
            
            downloadFile(htmlContent, 'نتائج_التحليل.html', 'text/html');
            showMessage('تم تصدير ملف HTML بنجاح', 'success');
        }

        function exportToTXT() {
            if (processedResults.length === 0) {
                showMessage('لا توجد نتائج للتصدير', 'warning');
                return;
            }
            
            let txtContent = `نتائج تحليل وتعديل الملفات\n`;
            txtContent += `تاريخ الإنشاء: ${new Date().toLocaleString('ar-SA')}\n`;
            txtContent += `عدد الملفات: ${processedResults.length}\n`;
            txtContent += `${'='.repeat(50)}\n\n`;
            
            processedResults.forEach((result, index) => {
                txtContent += `ملف ${index + 1}: ${result.originalName}\n`;
                txtContent += `نوع الملف: ${result.fileType?.extension || 'غير محدد'}\n`;
                txtContent += `الحجم: ${formatFileSize(result.size || 0)}\n`;
                txtContent += `${'-'.repeat(30)}\n`;
                txtContent += `المحتوى المعدل:\n`;
                txtContent += `${result.processedContent || ''}\n`;
                txtContent += `${'='.repeat(50)}\n\n`;
            });
            
            downloadFile(txtContent, 'نتائج_التحليل.txt', 'text/plain');
            showMessage('تم تصدير ملف TXT بنجاح', 'success');
        }

        function copyToClipboard() {
            if (processedResults.length === 0) {
                showMessage('لا توجد نتائج للنسخ', 'warning');
                return;
            }
            
            let content = '';
            processedResults.forEach((result, index) => {
                content += `=== ${result.originalName} ===\n`;
                content += `${result.processedContent || ''}\n\n`;
            });
            
            navigator.clipboard.writeText(content).then(() => {
                showMessage('تم نسخ النتائج إلى الحافظة', 'success');
            }).catch(() => {
                showMessage('فشل في نسخ النتائج', 'error');
            });
        }

        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType + ';charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function showMessage(message, type) {
            const notification = document.createElement('div');
            notification.className = `alert alert-${getAlertClass(type)} notification`;
            notification.innerHTML = `
                <i class="fas fa-${getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        function getAlertClass(type) {
            const classes = {
                'success': 'success',
                'error': 'danger',
                'warning': 'warning',
                'info': 'info'
            };
            return classes[type] || 'info';
        }

        function getAlertIcon(type) {
            const icons = {
                'success': 'check-circle',
                'error': 'exclamation-circle',
                'warning': 'exclamation-triangle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        // Settings functions (simplified)
        function exportSettings() {
            const settings = {
                selectedPresets: Array.from(selectedPresets),
                customInstructions: document.getElementById('customInstructions').value,
                exportDate: new Date().toISOString()
            };
            
            downloadFile(JSON.stringify(settings, null, 2), 'إعدادات_الأداة.json', 'application/json');
            showMessage('تم تصدير الإعدادات بنجاح', 'success');
        }

        function importSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const settings = JSON.parse(e.target.result);
                            
                            if (settings.selectedPresets) {
                                selectedPresets = new Set(settings.selectedPresets);
                                renderPresetInstructions();
                            }
                            
                            if (settings.customInstructions) {
                                document.getElementById('customInstructions').value = settings.customInstructions;
                            }
                            
                            showMessage('تم استيراد الإعدادات بنجاح', 'success');
                        } catch (error) {
                            showMessage('خطأ في استيراد الإعدادات', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }
    </script>
</body>
</html>
