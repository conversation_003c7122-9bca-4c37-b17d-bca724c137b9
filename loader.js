// Advanced Module Loader for File Analyzer App

(function() {
    'use strict';
    
    // Module loader configuration
    const MODULES = [
        { name: 'CONFIG', file: 'config.js', required: true },
        { name: 'APIManager', file: 'api-manager.js', required: true, depends: ['CONFIG'] },
        { name: 'FileProcessor', file: 'file-processor.js', required: true, depends: ['CONFIG'] },
        { name: 'ContentSplitter', file: 'content-splitter.js', required: true, depends: ['CONFIG'] },
        { name: 'PresetInstructionsManager', file: 'preset-instructions.js', required: true, depends: ['CONFIG'] },
        { name: 'ExportManager', file: 'export-manager.js', required: true, depends: ['CONFIG'] },
        { name: 'SettingsManager', file: 'settings-manager.js', required: true, depends: ['CONFIG'] },
        { name: 'FileAnalyzerApp', file: 'script.js', required: true, depends: ['CONFIG', 'APIManager', 'FileProcessor', 'ContentSplitter', 'PresetInstructionsManager', 'ExportManager', 'SettingsManager'] }
    ];
    
    class ModuleLoader {
        constructor() {
            this.loadedModules = new Set();
            this.loadingModules = new Set();
            this.failedModules = new Set();
            this.callbacks = [];
            this.startTime = Date.now();
        }
        
        // Load all modules
        async loadAll() {
            try {
                console.log('🚀 بدء تحميل وحدات التطبيق...');
                
                // Load modules in dependency order
                for (const module of MODULES) {
                    await this.loadModule(module);
                }
                
                const loadTime = Date.now() - this.startTime;
                console.log(`✅ تم تحميل جميع الوحدات بنجاح في ${loadTime}ms`);
                
                // Execute callbacks
                this.callbacks.forEach(callback => {
                    try {
                        callback();
                    } catch (error) {
                        console.error('خطأ في تنفيذ callback:', error);
                    }
                });
                
                return true;
                
            } catch (error) {
                console.error('❌ فشل في تحميل الوحدات:', error);
                this.showLoadingError(error);
                return false;
            }
        }
        
        // Load single module
        async loadModule(module) {
            // Check if already loaded
            if (this.loadedModules.has(module.name)) {
                return true;
            }
            
            // Check if currently loading
            if (this.loadingModules.has(module.name)) {
                return this.waitForModule(module.name);
            }
            
            // Check dependencies
            if (module.depends) {
                for (const dep of module.depends) {
                    if (!this.loadedModules.has(dep)) {
                        const depModule = MODULES.find(m => m.name === dep);
                        if (depModule) {
                            await this.loadModule(depModule);
                        }
                    }
                }
            }
            
            this.loadingModules.add(module.name);
            
            try {
                await this.loadScript(module.file);
                
                // Wait for module to register itself
                await this.waitForModuleRegistration(module.name);
                
                this.loadedModules.add(module.name);
                this.loadingModules.delete(module.name);
                
                console.log(`✓ تم تحميل ${module.name}`);
                return true;
                
            } catch (error) {
                this.loadingModules.delete(module.name);
                this.failedModules.add(module.name);
                console.error(`❌ فشل في تحميل ${module.name}:`, error);
                throw error;
            }
        }
        
        // Load script file
        loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = () => reject(new Error(`فشل في تحميل ${src}`));
                document.head.appendChild(script);
            });
        }
        
        // Wait for module to register itself
        waitForModuleRegistration(moduleName, timeout = 5000) {
            return new Promise((resolve, reject) => {
                const startTime = Date.now();
                
                const checkRegistration = () => {
                    // Check if module is available globally
                    const moduleAvailable = this.isModuleAvailable(moduleName);
                    
                    if (moduleAvailable) {
                        resolve();
                        return;
                    }
                    
                    // Check timeout
                    if (Date.now() - startTime > timeout) {
                        reject(new Error(`انتهت مهلة انتظار تسجيل ${moduleName}`));
                        return;
                    }
                    
                    // Check again after short delay
                    setTimeout(checkRegistration, 50);
                };
                
                checkRegistration();
            });
        }
        
        // Check if module is available
        isModuleAvailable(moduleName) {
            switch (moduleName) {
                case 'CONFIG':
                    return typeof window.CONFIG !== 'undefined';
                case 'APIManager':
                    return typeof window.apiManager !== 'undefined';
                case 'FileProcessor':
                    return typeof window.fileProcessor !== 'undefined';
                case 'ContentSplitter':
                    return typeof window.contentSplitter !== 'undefined';
                case 'PresetInstructionsManager':
                    return typeof window.presetManager !== 'undefined';
                case 'ExportManager':
                    return typeof window.exportManager !== 'undefined';
                case 'SettingsManager':
                    return typeof window.settingsManager !== 'undefined';
                case 'FileAnalyzerApp':
                    return typeof window.FileAnalyzerApp !== 'undefined';
                default:
                    return typeof window[moduleName] !== 'undefined';
            }
        }
        
        // Wait for specific module
        waitForModule(moduleName, timeout = 10000) {
            return new Promise((resolve, reject) => {
                const startTime = Date.now();
                
                const checkModule = () => {
                    if (this.loadedModules.has(moduleName)) {
                        resolve();
                        return;
                    }
                    
                    if (this.failedModules.has(moduleName)) {
                        reject(new Error(`فشل في تحميل ${moduleName}`));
                        return;
                    }
                    
                    if (Date.now() - startTime > timeout) {
                        reject(new Error(`انتهت مهلة انتظار ${moduleName}`));
                        return;
                    }
                    
                    setTimeout(checkModule, 100);
                };
                
                checkModule();
            });
        }
        
        // Add callback for when all modules are loaded
        onReady(callback) {
            if (this.loadedModules.size === MODULES.length) {
                callback();
            } else {
                this.callbacks.push(callback);
            }
        }
        
        // Show loading error
        showLoadingError(error) {
            const errorHtml = `
                <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif; direction: rtl; max-width: 600px; margin: 0 auto;">
                    <h2 style="color: #dc2626;">خطأ في تحميل التطبيق</h2>
                    <p style="color: #991b1b; margin: 20px 0;">${error.message}</p>
                    
                    <div style="margin: 20px 0; padding: 15px; background: #fee2e2; border-radius: 6px; text-align: right;">
                        <h4 style="color: #991b1b; margin-bottom: 10px;">تفاصيل التحميل:</h4>
                        <p style="color: #7f1d1d; font-size: 14px;">
                            الوحدات المحملة: ${this.loadedModules.size}/${MODULES.length}<br>
                            الوحدات الفاشلة: ${Array.from(this.failedModules).join(', ') || 'لا يوجد'}
                        </p>
                    </div>
                    
                    <div style="margin-top: 30px;">
                        <button onclick="location.reload()" style="background: #2563eb; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 5px; font-size: 14px;">
                            إعادة تحميل الصفحة
                        </button>
                        <button onclick="window.open('test.html', '_blank')" style="background: #059669; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin: 5px; font-size: 14px;">
                            فتح صفحة الاختبار
                        </button>
                    </div>
                    
                    <div style="margin-top: 20px; padding: 15px; background: #f3f4f6; border-radius: 6px; text-align: right;">
                        <h4 style="color: #374151; margin-bottom: 10px;">نصائح لحل المشكلة:</h4>
                        <ul style="color: #6b7280; text-align: right; font-size: 14px;">
                            <li>تأكد من اتصالك بالإنترنت</li>
                            <li>امسح ذاكرة التخزين المؤقت للمتصفح</li>
                            <li>تأكد من وجود جميع الملفات في نفس المجلد</li>
                            <li>جرب متصفح آخر</li>
                            <li>تأكد من تفعيل JavaScript</li>
                        </ul>
                    </div>
                </div>
            `;
            
            document.body.innerHTML = errorHtml;
        }
        
        // Get loading status
        getStatus() {
            return {
                loaded: Array.from(this.loadedModules),
                loading: Array.from(this.loadingModules),
                failed: Array.from(this.failedModules),
                total: MODULES.length,
                progress: Math.round((this.loadedModules.size / MODULES.length) * 100)
            };
        }
    }
    
    // Create global loader instance
    window.moduleLoader = new ModuleLoader();
    
    // Auto-start loading when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.moduleLoader.loadAll();
        });
    } else {
        window.moduleLoader.loadAll();
    }
    
})();
